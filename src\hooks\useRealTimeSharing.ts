'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { RealTimeUser, RealTimeMessage, TimerAction, BossAction, TimerState, Boss } from '@/types/boss';
import { supabaseService, RoomData } from '@/services/supabaseService';

const USER_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
];

export function useRealTimeSharing() {
  const [isConnected, setIsConnected] = useState(false);
  const [isHost, setIsHost] = useState(false);
  const [roomId, setRoomId] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<RealTimeUser | null>(null);
  const [connectedUsers, setConnectedUsers] = useState<RealTimeUser[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [error, setError] = useState<string | null>(null);

  // Refs for managing subscriptions and intervals
  const subscriptionRef = useRef<(() => void) | null>(null);
  const messageHandlersRef = useRef<Array<(message: RealTimeMessage) => void>>([]);
  const lastMessageCheckRef = useRef<string>(new Date().toISOString());
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Generate a simple room ID
  const generateRoomId = useCallback(() => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }, []);

  // Generate or get persistent user ID and color
  const generateUser = useCallback((name: string): RealTimeUser => {
    // Try to get existing user ID from localStorage for this name
    const storageKey = `user-${name.toLowerCase().replace(/\s+/g, '-')}`;
    let userId = localStorage.getItem(storageKey);

    if (!userId) {
      // Generate new user ID and store it
      userId = Math.random().toString(36).substring(2, 15);
      localStorage.setItem(storageKey, userId);
    }

    const color = USER_COLORS[Math.floor(Math.random() * USER_COLORS.length)];
    return {
      id: userId,
      name,
      color,
      joinedAt: new Date(),
    };
  }, []);

  // Add message handler
  const addMessageHandler = useCallback((handler: (message: RealTimeMessage) => void) => {
    messageHandlersRef.current.push(handler);
    return () => {
      messageHandlersRef.current = messageHandlersRef.current.filter(h => h !== handler);
    };
  }, []);

  // Send message to all connected users using Supabase
  const broadcastMessage = useCallback(async (message: RealTimeMessage) => {
    if (!roomId) return;

    try {
      console.log('Broadcasting message:', message.type, 'from:', message.userName);
      await supabaseService.sendMessage(roomId, message);
    } catch (error) {
      console.error('Failed to broadcast message:', error);
      setError('Failed to send message');
    }
  }, [roomId]);



  // Setup real-time subscriptions
  const setupSubscriptions = useCallback((roomId: string) => {
    if (subscriptionRef.current) {
      subscriptionRef.current();
    }

    subscriptionRef.current = supabaseService.subscribeToRoom(roomId, {
      onUserJoin: (user) => {
        console.log('User joined:', user.name);
        setConnectedUsers(prev => {
          if (prev.find(u => u.id === user.id)) return prev;
          return [...prev, user];
        });
      },
      onUserLeave: (userId) => {
        console.log('User left:', userId);
        setConnectedUsers(prev => prev.filter(u => u.id !== userId));
      },
      onMessage: (message) => {
        // Only process messages from other users
        if (message.userId !== currentUser?.id) {
          console.log('Received message:', message.type, 'from:', message.userName);
          messageHandlersRef.current.forEach(handler => handler(message));
        }
      },
      onStateChange: (stateType, stateData) => {
        console.log('Room state changed:', stateType, 'data keys:', Object.keys(stateData || {}));
        // Broadcast state change as a message to all connected clients
        if (currentUser) {
          const stateMessage: RealTimeMessage = {
            type: 'state_update' as any,
            userId: currentUser.id,
            userName: currentUser.name,
            timestamp: new Date(),
            data: { stateType, stateData },
          };
          messageHandlersRef.current.forEach(handler => handler(stateMessage));
        }
      },
    });
  }, [currentUser?.id]);

  // Update user heartbeat
  const updateHeartbeat = useCallback(async () => {
    if (!roomId || !currentUser) return;

    try {
      await supabaseService.updateUserHeartbeat(roomId, currentUser.id);
    } catch (error) {
      console.error('Failed to update heartbeat:', error);

      // If it's a network error, set connection status to indicate issues
      if (error instanceof Error && error.message.includes('Network connection failed')) {
        setConnectionStatus('connecting');
        setError('Connection issues detected. Attempting to reconnect...');

        // Try to test connection
        setTimeout(async () => {
          try {
            const connectionTest = await supabaseService.testConnection();
            if (connectionTest.success) {
              setConnectionStatus('connected');
              setError(null);
            } else {
              setError(`Connection failed: ${connectionTest.error}`);
            }
          } catch (testError) {
            console.error('Connection test failed:', testError);
            setError('Unable to connect to the server. Please check your internet connection.');
          }
        }, 5000); // Test connection after 5 seconds
      }
    }
  }, [roomId, currentUser]);

  // Create a new room (host)
  const createRoom = useCallback(async (userName: string, password?: string): Promise<string> => {
    try {
      console.log('Creating room for user:', userName, 'with password:', !!password);
      setConnectionStatus('connecting');
      setError(null);

      // Test connection first
      console.log('Testing Supabase connection...');
      const connectionTest = await supabaseService.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Connection failed: ${connectionTest.error}`);
      }
      console.log('Connection test successful');

      const newRoomId = generateRoomId();
      const user = generateUser(userName);

      console.log('Generated room ID:', newRoomId, 'User:', user);

      // Create room in Supabase
      await supabaseService.createRoom({
        id: newRoomId,
        host_id: user.id,
        host_name: user.name,
        password: password,
      });

      // Add user to room
      await supabaseService.addUserToRoom(newRoomId, user);

      setRoomId(newRoomId);
      setCurrentUser(user);
      setIsHost(true);
      setConnectedUsers([user]);
      setIsConnected(true);
      setConnectionStatus('connected');

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId: newRoomId,
        user,
        isHost: true,
      }));

      // Setup real-time subscriptions
      setupSubscriptions(newRoomId);

      // Start heartbeat interval
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      heartbeatIntervalRef.current = setInterval(updateHeartbeat, 30000); // 30 seconds

      console.log('Room created successfully:', newRoomId);
      return newRoomId;
    } catch (error) {
      console.error('Failed to create room:', error);
      setError(error instanceof Error ? error.message : 'Failed to create room');
      setConnectionStatus('disconnected');
      throw error;
    }
  }, [generateRoomId, generateUser, setupSubscriptions, updateHeartbeat]);

  // Join an existing room
  const joinRoom = useCallback(async (roomId: string, userName: string, password?: string): Promise<void> => {
    try {
      console.log('Joining room:', roomId, 'as user:', userName, 'with password:', !!password);
      setConnectionStatus('connecting');
      setError(null);

      // Test connection first
      console.log('Testing Supabase connection...');
      const connectionTest = await supabaseService.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Connection failed: ${connectionTest.error}`);
      }
      console.log('Connection test successful');

      // Check if room exists
      const roomData = await supabaseService.getRoomData(roomId);
      if (!roomData) {
        setError('Room not found. Please check the room code and make sure the host has created the room.');
        setConnectionStatus('disconnected');
        return;
      }

      // Check if room is active
      if (!roomData.is_active) {
        setError('This room has been closed by the host.');
        setConnectionStatus('disconnected');
        return;
      }

      // Check password if required
      if (roomData.has_password) {
        if (!password) {
          setError('This room requires a password.');
          setConnectionStatus('disconnected');
          return;
        }

        const isPasswordValid = await supabaseService.verifyRoomPassword(roomId, password);
        if (!isPasswordValid) {
          setError('Incorrect password.');
          setConnectionStatus('disconnected');
          return;
        }
      }

      const user = generateUser(userName);
      console.log('Generated user for joining:', user);

      // Add user to room
      await supabaseService.addUserToRoom(roomId, user);

      // Get current users
      const currentUsers = await supabaseService.getRoomUsers(roomId);

      setRoomId(roomId);
      setCurrentUser(user);
      setIsHost(false);
      setConnectedUsers(currentUsers);
      setIsConnected(true);
      setConnectionStatus('connected');

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId,
        user,
        isHost: false,
      }));

      // Setup real-time subscriptions
      setupSubscriptions(roomId);

      // Start heartbeat interval
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      heartbeatIntervalRef.current = setInterval(updateHeartbeat, 30000); // 30 seconds

      // Send user join message
      const joinMessage: RealTimeMessage = {
        type: 'user_join',
        userId: user.id,
        userName: user.name,
        timestamp: new Date(),
        data: {},
      };
      await broadcastMessage(joinMessage);

      console.log('Successfully joined room:', roomId);
    } catch (error) {
      console.error('Failed to join room:', error);
      setError(error instanceof Error ? error.message : 'Failed to join room');
      setConnectionStatus('disconnected');
    }
  }, [generateUser, setupSubscriptions, updateHeartbeat, broadcastMessage]);

  // Leave the current room
  const leaveRoom = useCallback(async () => {
    // Send leave message before disconnecting
    if (currentUser) {
      const leaveMessage: RealTimeMessage = {
        type: 'user_leave',
        userId: currentUser.id,
        userName: currentUser.name,
        timestamp: new Date(),
        data: {},
      };
      await broadcastMessage(leaveMessage);
    }

    // Remove user from room
    if (roomId && currentUser) {
      try {
        await supabaseService.removeUserFromRoom(roomId, currentUser.id);
      } catch (error) {
        console.error('Failed to remove user from room:', error);
      }
    }

    // Cleanup subscriptions
    if (subscriptionRef.current) {
      subscriptionRef.current();
      subscriptionRef.current = null;
    }

    // Stop heartbeat interval
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    // Reset state
    setIsConnected(false);
    setIsHost(false);
    setRoomId(null);
    setCurrentUser(null);
    setConnectedUsers([]);
    setConnectionStatus('disconnected');
    setError(null);

    // Clear localStorage
    localStorage.removeItem('realtime-room');
  }, [currentUser, roomId, broadcastMessage]);

  // Close room (host only)
  const closeRoom = useCallback(async () => {
    if (!isHost || !roomId || !currentUser) return;

    try {
      // Send room closed message to all users
      const closeMessage: RealTimeMessage = {
        type: 'room_closed',
        userId: currentUser.id,
        userName: currentUser.name,
        timestamp: new Date(),
        data: { reason: 'Host closed the room' },
      };
      await broadcastMessage(closeMessage);

      // Close room in Supabase
      await supabaseService.closeRoom(roomId, currentUser.id);

      // Leave the room
      await leaveRoom();
    } catch (error) {
      console.error('Failed to close room:', error);
    }
  }, [isHost, roomId, currentUser, broadcastMessage, leaveRoom]);

  // Send a message to all connected users
  const sendMessage = useCallback((type: RealTimeMessage['type'], data: TimerAction | BossAction | { timerState?: TimerState; customBosses?: Boss[] } | Record<string, unknown>) => {
    if (!currentUser) return;

    const message: RealTimeMessage = {
      type,
      userId: currentUser.id,
      userName: currentUser.name,
      timestamp: new Date(),
      data,
    };

    broadcastMessage(message);
  }, [currentUser, broadcastMessage]);

  // Save room state to database
  const saveRoomState = useCallback(async (stateType: 'timer_state' | 'custom_bosses', stateData: any) => {
    if (!roomId || !currentUser) return;

    try {
      await supabaseService.saveRoomState(roomId, stateType, stateData, currentUser.id);
    } catch (error) {
      console.error('Failed to save room state:', error);
    }
  }, [roomId, currentUser]);

  // Get room state from database
  const getRoomState = useCallback(async (stateType?: 'timer_state' | 'custom_bosses') => {
    if (!roomId) return [];

    try {
      return await supabaseService.getRoomState(roomId, stateType);
    } catch (error) {
      console.error('Failed to get room state:', error);
      return [];
    }
  }, [roomId]);

  // Initialize from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('realtime-room');
    if (saved) {
      try {
        const { roomId: savedRoomId, user, isHost: savedIsHost } = JSON.parse(saved);
        console.log('Attempting to restore room:', savedRoomId, 'for user:', user.name);

        // Check if room still exists
        supabaseService.getRoomData(savedRoomId).then(async (roomData) => {
          if (roomData && roomData.is_active) {
            console.log('Room found and active, restoring connection...');

            setRoomId(savedRoomId);
            setCurrentUser(user);
            setIsHost(savedIsHost);
            setConnectionStatus('connecting');

            try {
              // Re-add user to room (this will update their status)
              await supabaseService.addUserToRoom(savedRoomId, user);

              // Setup subscriptions
              setupSubscriptions(savedRoomId);

              // Start heartbeat
              heartbeatIntervalRef.current = setInterval(updateHeartbeat, 30000);

              // Get current users
              const users = await supabaseService.getRoomUsers(savedRoomId);
              setConnectedUsers(users);

              setIsConnected(true);
              setConnectionStatus('connected');
              setError(null);

              console.log('Room restoration successful');
            } catch (restoreError) {
              console.error('Failed to restore room connection:', restoreError);
              setError('Failed to restore room connection');
              setConnectionStatus('disconnected');
              localStorage.removeItem('realtime-room');
            }
          } else {
            console.log('Room no longer exists or is inactive, clearing saved data');
            // Room no longer exists, clear saved data
            localStorage.removeItem('realtime-room');
            setConnectionStatus('disconnected');
          }
        }).catch(error => {
          console.error('Failed to restore room:', error);

          // Check if it's a network error
          if (error instanceof Error && error.message.includes('Network connection failed')) {
            setError('Connection issues detected. Please check your internet connection.');
            setConnectionStatus('disconnected');
            // Don't remove localStorage data for network errors - allow retry
          } else {
            localStorage.removeItem('realtime-room');
            setConnectionStatus('disconnected');
          }
        });
      } catch (error) {
        console.error('Failed to restore room from localStorage:', error);
        localStorage.removeItem('realtime-room');
        setConnectionStatus('disconnected');
      }
    }
  }, [setupSubscriptions, updateHeartbeat]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup subscriptions
      if (subscriptionRef.current) {
        subscriptionRef.current();
      }

      // Stop heartbeat interval
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
    };
  }, []);

  return {
    // State
    isConnected,
    isHost,
    roomId,
    currentUser,
    connectedUsers,
    connectionStatus,
    error,

    // Actions
    createRoom,
    joinRoom,
    leaveRoom,
    closeRoom,
    sendMessage,
    addMessageHandler,
    saveRoomState,
    getRoomState,

  };
}
