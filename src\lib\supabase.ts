import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

// Validate environment variables
if (typeof window !== 'undefined') {
  if (!supabaseUrl || supabaseUrl === 'https://your-project.supabase.co') {
    console.error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
  }
  if (!supabaseAnonKey || supabaseAnonKey === 'your-anon-key') {
    console.error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');
  }
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey, {
  auth: {
    persistSession: false, // We're not using auth sessions for this app
    autoRefreshToken: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  },
});

// Database types
export interface Database {
  public: {
    Tables: {
      rooms: {
        Row: {
          id: string;
          host_id: string;
          host_name: string;
          password_hash: string | null;
          has_password: boolean;
          is_active: boolean;
          created_at: string;
          updated_at: string;
          last_activity: string;
        };
        Insert: {
          id: string;
          host_id: string;
          host_name: string;
          password_hash?: string | null;
          has_password?: boolean;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          last_activity?: string;
        };
        Update: {
          id?: string;
          host_id?: string;
          host_name?: string;
          password_hash?: string | null;
          has_password?: boolean;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          last_activity?: string;
        };
      };
      room_users: {
        Row: {
          id: string;
          room_id: string;
          user_id: string;
          user_name: string;
          joined_at: string;
          last_seen: string;
          is_online: boolean;
        };
        Insert: {
          id?: string;
          room_id: string;
          user_id: string;
          user_name: string;
          joined_at?: string;
          last_seen?: string;
          is_online?: boolean;
        };
        Update: {
          id?: string;
          room_id?: string;
          user_id?: string;
          user_name?: string;
          joined_at?: string;
          last_seen?: string;
          is_online?: boolean;
        };
      };
      room_messages: {
        Row: {
          id: string;
          room_id: string;
          user_id: string;
          user_name: string;
          message_type: string;
          message_data: any;
          created_at: string;
        };
        Insert: {
          id?: string;
          room_id: string;
          user_id: string;
          user_name: string;
          message_type: string;
          message_data?: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          room_id?: string;
          user_id?: string;
          user_name?: string;
          message_type?: string;
          message_data?: any;
          created_at?: string;
        };
      };
    };
  };
}
