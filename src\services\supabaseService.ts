import { supabase } from '@/lib/supabase';
import { RealTimeUser, RealTimeMessage } from '@/types/boss';

export interface RoomData {
  id: string;
  host_id: string;
  host_name: string;
  password_hash?: string;
  has_password: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_activity: string;
  users?: RealTimeUser[];
}

export interface RoomMessage {
  id: string;
  room_id: string;
  user_id: string;
  user_name: string;
  message_type: string;
  message_data: any;
  created_at: string;
}

export interface RoomState {
  id: string;
  room_id: string;
  state_type: 'timer_state' | 'custom_bosses';
  state_data: any;
  updated_at: string;
  updated_by: string;
}

// Simple password hashing (for demo purposes - use proper hashing in production)
const hashPassword = (password: string): string => {
  return btoa(password + 'l2m-salt');
};

const verifyPassword = (password: string, hash: string): boolean => {
  return hashPassword(password) === hash;
};

export class SupabaseService {
  // Test connection to Supabase
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Testing Supabase connection...');
      console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
      console.log('Supabase Key exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

      const { error } = await supabase
        .from('rooms')
        .select('count')
        .limit(1);

      if (error) {
        console.error('Supabase connection test failed:', error);
        return { success: false, error: error.message };
      }

      console.log('Supabase connection test successful');
      return { success: true };
    } catch (error) {
      console.error('Supabase connection test error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown connection error'
      };
    }
  }

  // Create a new room
  async createRoom(roomData: {
    id: string;
    host_id: string;
    host_name: string;
    password?: string;
  }): Promise<RoomData> {
    try {
      const { data, error } = await supabase
        .from('rooms')
        .insert({
          id: roomData.id,
          host_id: roomData.host_id,
          host_name: roomData.host_name,
          password_hash: roomData.password ? hashPassword(roomData.password) : null,
          has_password: !!roomData.password,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_activity: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating room:', error);

        // Check for network errors
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }

        // Check for duplicate room ID
        if (error.code === '23505') {
          throw new Error('Room ID already exists. Please try again with a different room code.');
        }

        throw new Error(`Failed to create room: ${error.message}`);
      }

      return {
        id: data.id,
        host_id: data.host_id,
        host_name: data.host_name,
        password_hash: data.password_hash || undefined,
        has_password: data.has_password,
        is_active: data.is_active,
        created_at: data.created_at,
        updated_at: data.updated_at,
        last_activity: data.last_activity,
      };
    } catch (error) {
      console.error('Error in createRoom:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to create room: Unknown error');
    }
  }

  // Get room data
  async getRoomData(roomId: string): Promise<RoomData | null> {
    try {
      const { data, error } = await supabase
        .from('rooms')
        .select('*')
        .eq('id', roomId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Room not found
        }

        // Handle 406 Not Acceptable error
        if (error.code === 'PGRST106' || error.message.includes('406')) {
          console.warn('406 error when getting room data, trying alternative approach');
          // Try without .single() as fallback
          const { data: rooms, error: fallbackError } = await supabase
            .from('rooms')
            .select('*')
            .eq('id', roomId)
            .eq('is_active', true)
            .limit(1);

          if (fallbackError) {
            console.error('Fallback query also failed:', fallbackError);
            throw new Error(`Failed to get room data: ${fallbackError.message}`);
          }

          if (!rooms || rooms.length === 0) {
            return null;
          }

          const data = rooms[0];
          return {
            id: data.id,
            host_id: data.host_id,
            host_name: data.host_name,
            password_hash: data.password_hash || undefined,
            has_password: data.has_password,
            is_active: data.is_active,
            created_at: data.created_at,
            updated_at: data.updated_at,
            last_activity: data.last_activity,
          };
        }

        console.error('Error getting room data:', error);

        // Check for network errors
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }

        throw new Error(`Failed to get room data: ${error.message}`);
      }

      return {
        id: data.id,
        host_id: data.host_id,
        host_name: data.host_name,
        password_hash: data.password_hash || undefined,
        has_password: data.has_password,
        is_active: data.is_active,
        created_at: data.created_at,
        updated_at: data.updated_at,
        last_activity: data.last_activity,
      };
    } catch (error) {
      console.error('Error in getRoomData:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to get room data: Unknown error');
    }
  }

  // Verify room password
  async verifyRoomPassword(roomId: string, password: string): Promise<boolean> {
    const roomData = await this.getRoomData(roomId);
    if (!roomData || !roomData.has_password || !roomData.password_hash) {
      return !roomData?.has_password; // Return true if no password required
    }

    return verifyPassword(password, roomData.password_hash);
  }

  // Add user to room
  async addUserToRoom(roomId: string, user: RealTimeUser): Promise<void> {
    try {
      // First try to update existing user (without .single() to avoid 406 error)
      const { data: existingUsers, error: selectError } = await supabase
        .from('room_users')
        .select('id')
        .eq('room_id', roomId)
        .eq('user_id', user.id)
        .limit(1);

      if (selectError) {
        console.error('Error checking existing user:', selectError);
        throw new Error(`Failed to check existing user: ${selectError.message}`);
      }

      if (existingUsers && existingUsers.length > 0) {
        // User exists, just update their status
        const { error } = await supabase
          .from('room_users')
          .update({
            user_name: user.name,
            last_seen: new Date().toISOString(),
            is_online: true,
          })
          .eq('room_id', roomId)
          .eq('user_id', user.id);

        if (error) {
          console.error('Error updating user in room:', error);
          throw new Error(`Failed to update user in room: ${error.message}`);
        }
      } else {
        // User doesn't exist, insert new record
        const { error } = await supabase
          .from('room_users')
          .insert({
            room_id: roomId,
            user_id: user.id,
            user_name: user.name,
            joined_at: new Date().toISOString(),
            last_seen: new Date().toISOString(),
            is_online: true,
          });

        if (error) {
          console.error('Error adding user to room:', error);
          throw new Error(`Failed to add user to room: ${error.message}`);
        }
      }

      // Update room activity
      await this.updateRoomActivity(roomId);
    } catch (error) {
      console.error('Error in addUserToRoom:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to add user to room: Unknown error');
    }
  }

  // Update user heartbeat (for existing users)
  async updateUserHeartbeat(roomId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('room_users')
        .update({
          last_seen: new Date().toISOString(),
          is_online: true,
        })
        .eq('room_id', roomId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error updating user heartbeat:', error);

        // Check for network errors
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }

        throw new Error(`Failed to update user heartbeat: ${error.message}`);
      }

      // Update room activity
      await this.updateRoomActivity(roomId);
    } catch (error) {
      console.error('Error in updateUserHeartbeat:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to update user heartbeat: Unknown error');
    }
  }

  // Remove user from room
  async removeUserFromRoom(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('room_users')
      .update({ is_online: false, last_seen: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error removing user from room:', error);
      throw new Error(`Failed to remove user from room: ${error.message}`);
    }

    // Update room activity
    await this.updateRoomActivity(roomId);
  }

  // Get room users
  async getRoomUsers(roomId: string): Promise<RealTimeUser[]> {
    const { data, error } = await supabase
      .from('room_users')
      .select('*')
      .eq('room_id', roomId)
      .eq('is_online', true)
      .order('joined_at', { ascending: true });

    if (error) {
      console.error('Error getting room users:', error);
      throw new Error(`Failed to get room users: ${error.message}`);
    }

    return data.map(user => ({
      id: user.user_id,
      name: user.user_name,
      joinedAt: new Date(user.joined_at),
    }));
  }

  // Send message to room
  async sendMessage(roomId: string, message: RealTimeMessage): Promise<void> {
    const { error } = await supabase
      .from('room_messages')
      .insert({
        room_id: roomId,
        user_id: message.userId,
        user_name: message.userName,
        message_type: message.type,
        message_data: message.data,
        created_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error sending message:', error);
      throw new Error(`Failed to send message: ${error.message}`);
    }

    // Update room activity
    await this.updateRoomActivity(roomId);
  }

  // Get messages since timestamp
  async getMessagesSince(roomId: string, since: string): Promise<RoomMessage[]> {
    const { data, error } = await supabase
      .from('room_messages')
      .select('*')
      .eq('room_id', roomId)
      .gt('created_at', since)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error getting messages:', error);
      throw new Error(`Failed to get messages: ${error.message}`);
    }

    return data;
  }

  // Save room state (timer state or custom bosses)
  async saveRoomState(roomId: string, stateType: 'timer_state' | 'custom_bosses', stateData: any, updatedBy: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('room_state')
        .upsert({
          room_id: roomId,
          state_type: stateType,
          state_data: stateData,
          updated_at: new Date().toISOString(),
          updated_by: updatedBy,
        }, {
          onConflict: 'room_id,state_type',
          ignoreDuplicates: false
        });

      if (error) {
        console.error('Error saving room state:', error);
        throw new Error(`Failed to save room state: ${error.message}`);
      }
    } catch (error) {
      console.error('Error in saveRoomState:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to save room state: Unknown error');
    }
  }

  // Get room state
  async getRoomState(roomId: string, stateType?: 'timer_state' | 'custom_bosses'): Promise<RoomState[]> {
    try {
      let query = supabase
        .from('room_state')
        .select('*')
        .eq('room_id', roomId);

      if (stateType) {
        query = query.eq('state_type', stateType);
      }

      const { data, error } = await query.order('updated_at', { ascending: false });

      if (error) {
        console.error('Error getting room state:', error);
        throw new Error(`Failed to get room state: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error in getRoomState:', error);

      if (error instanceof Error) {
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          throw new Error('Network connection failed. Please check your internet connection and try again.');
        }
        throw error;
      }

      throw new Error('Failed to get room state: Unknown error');
    }
  }

  // Update room activity
  async updateRoomActivity(roomId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('rooms')
        .update({
          last_activity: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', roomId);

      if (error) {
        console.error('Error updating room activity:', error);

        // Don't throw for room activity updates - they're not critical
        // Just log the error and continue
        if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
          console.warn('Network connection issue while updating room activity - will retry later');
        }
      }
    } catch (error) {
      console.error('Error in updateRoomActivity:', error);
      // Don't throw - room activity updates are not critical
    }
  }

  // Close room (host only)
  async closeRoom(roomId: string, hostId: string): Promise<void> {
    const { error } = await supabase
      .from('rooms')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', roomId)
      .eq('host_id', hostId);

    if (error) {
      console.error('Error closing room:', error);
      throw new Error(`Failed to close room: ${error.message}`);
    }

    // Mark all users as offline
    await supabase
      .from('room_users')
      .update({ is_online: false, last_seen: new Date().toISOString() })
      .eq('room_id', roomId);
  }

  // Subscribe to room changes
  subscribeToRoom(roomId: string, callbacks: {
    onUserJoin?: (user: RealTimeUser) => void;
    onUserLeave?: (userId: string) => void;
    onMessage?: (message: RealTimeMessage) => void;
    onStateChange?: (stateType: string, stateData: any) => void;
  }) {
    // Subscribe to user changes
    const userSubscription = supabase
      .channel(`room-users-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'room_users',
          filter: `room_id=eq.${roomId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT' && payload.new.is_online) {
            callbacks.onUserJoin?.({
              id: payload.new.user_id,
              name: payload.new.user_name,
              joinedAt: new Date(payload.new.joined_at),
            });
          } else if (payload.eventType === 'UPDATE' && !payload.new.is_online) {
            callbacks.onUserLeave?.(payload.new.user_id);
          }
        }
      )
      .subscribe();

    // Subscribe to messages
    const messageSubscription = supabase
      .channel(`room-messages-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'room_messages',
          filter: `room_id=eq.${roomId}`,
        },
        (payload) => {
          callbacks.onMessage?.({
            type: payload.new.message_type as any,
            userId: payload.new.user_id,
            userName: payload.new.user_name,
            timestamp: new Date(payload.new.created_at),
            data: payload.new.message_data,
          });
        }
      )
      .subscribe();

    // Subscribe to room state changes
    const stateSubscription = supabase
      .channel(`room-state-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'room_state',
          filter: `room_id=eq.${roomId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            callbacks.onStateChange?.(payload.new.state_type, payload.new.state_data);
          }
        }
      )
      .subscribe();

    return () => {
      userSubscription.unsubscribe();
      messageSubscription.unsubscribe();
      stateSubscription.unsubscribe();
    };
  }
}

export const supabaseService = new SupabaseService();
