// Room management utility script
// Run with: node room-manager.js [command] [args...]

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function listRooms() {
  console.log('📋 Active Rooms:');
  const { data, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No active rooms found.');
    return;
  }

  data.forEach(room => {
    console.log(`\n🏠 Room: ${room.id}`);
    console.log(`   Host: ${room.host_name} (${room.host_id})`);
    console.log(`   Password: ${room.has_password ? 'Yes' : 'No'}`);
    console.log(`   Created: ${new Date(room.created_at).toLocaleString()}`);
    console.log(`   Last Activity: ${new Date(room.last_activity).toLocaleString()}`);
  });
}

async function listRoomUsers(roomId) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`👥 Users in room ${roomId}:`);
  const { data, error } = await supabase
    .from('room_users')
    .select('*')
    .eq('room_id', roomId)
    .order('joined_at', { ascending: true });

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No users found in this room.');
    return;
  }

  data.forEach(user => {
    const status = user.is_online ? '🟢 Online' : '🔴 Offline';
    console.log(`\n${status} ${user.user_name} (${user.user_id})`);
    console.log(`   Joined: ${new Date(user.joined_at).toLocaleString()}`);
    console.log(`   Last Seen: ${new Date(user.last_seen).toLocaleString()}`);
  });
}

async function cleanupRoom(roomId) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`🧹 Cleaning up room ${roomId} (marking all users as offline)...`);

  // Mark all users as offline
  const { error: usersError } = await supabase
    .from('room_users')
    .update({ is_online: false, last_seen: new Date().toISOString() })
    .eq('room_id', roomId);

  if (usersError) {
    console.error('❌ Error updating users:', usersError);
    return;
  }

  console.log('✅ All users marked as offline');
  console.log('Note: Room deactivation requires host permissions and is handled by the application.');
}

async function removeUser(roomId, userId) {
  if (!roomId || !userId) {
    console.error('❌ Please provide both room ID and user ID');
    return;
  }

  console.log(`🚪 Removing user ${userId} from room ${roomId}...`);

  const { error } = await supabase
    .from('room_users')
    .update({ is_online: false, last_seen: new Date().toISOString() })
    .eq('room_id', roomId)
    .eq('user_id', userId);

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  console.log('✅ User removed successfully');
}

async function removeDuplicateUsers(roomId) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`🧹 Removing duplicate users from room ${roomId}...`);

  // Get all users in the room
  const { data: users, error } = await supabase
    .from('room_users')
    .select('*')
    .eq('room_id', roomId)
    .eq('is_online', true)
    .order('joined_at', { ascending: true });

  if (error) {
    console.error('❌ Error getting users:', error);
    return;
  }

  if (!users || users.length === 0) {
    console.log('No users found in room.');
    return;
  }

  // Group users by name
  const usersByName = {};
  users.forEach(user => {
    if (!usersByName[user.user_name]) {
      usersByName[user.user_name] = [];
    }
    usersByName[user.user_name].push(user);
  });

  let removedCount = 0;

  // For each name, keep only the most recent user (last joined)
  for (const [userName, userList] of Object.entries(usersByName)) {
    if (userList.length > 1) {
      console.log(`Found ${userList.length} users named "${userName}"`);

      // Sort by joined_at and keep the last one
      userList.sort((a, b) => new Date(a.joined_at) - new Date(b.joined_at));
      const usersToRemove = userList.slice(0, -1); // Remove all but the last one

      for (const userToRemove of usersToRemove) {
        console.log(`  Removing duplicate: ${userToRemove.user_id} (joined: ${new Date(userToRemove.joined_at).toLocaleString()})`);

        const { error: removeError } = await supabase
          .from('room_users')
          .update({ is_online: false, last_seen: new Date().toISOString() })
          .eq('id', userToRemove.id);

        if (removeError) {
          console.error(`  ❌ Error removing user ${userToRemove.user_id}:`, removeError);
        } else {
          removedCount++;
        }
      }

      console.log(`  Kept: ${userList[userList.length - 1].user_id} (joined: ${new Date(userList[userList.length - 1].joined_at).toLocaleString()})`);
    }
  }

  console.log(`✅ Removed ${removedCount} duplicate users`);
}

async function getRoomMessages(roomId, limit = 10) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`💬 Recent messages in room ${roomId}:`);
  const { data, error } = await supabase
    .from('room_messages')
    .select('*')
    .eq('room_id', roomId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No messages found in this room.');
    return;
  }

  data.reverse().forEach(msg => {
    const time = new Date(msg.created_at).toLocaleTimeString();
    console.log(`\n[${time}] ${msg.user_name}: ${msg.message_type}`);
    if (msg.message_data && Object.keys(msg.message_data).length > 0) {
      console.log(`   Data: ${JSON.stringify(msg.message_data, null, 2)}`);
    }
  });
}

function showHelp() {
  console.log(`
🛠️  Room Manager Utility

Usage: node room-manager.js [command] [args...]

Commands:
  list-rooms                     List all active rooms
  list-users <roomId>           List users in a specific room
  cleanup-room <roomId>         Mark room as inactive and all users as offline
  remove-user <roomId> <userId> Remove a specific user from a room
  remove-duplicates <roomId>    Remove duplicate users (same name) from a room
  messages <roomId> [limit]     Show recent messages in a room (default: 10)
  help                          Show this help message

Examples:
  node room-manager.js list-rooms
  node room-manager.js list-users AA600D
  node room-manager.js remove-duplicates AA600D
  node room-manager.js cleanup-room AA600D
  node room-manager.js remove-user AA600D rlz0bjx80u
  node room-manager.js messages AA600D 20
`);
}

// Main execution
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];
const arg3 = process.argv[5];

switch (command) {
  case 'list-rooms':
    await listRooms();
    break;
  case 'list-users':
    await listRoomUsers(arg1);
    break;
  case 'cleanup-room':
    await cleanupRoom(arg1);
    break;
  case 'remove-user':
    await removeUser(arg1, arg2);
    break;
  case 'remove-duplicates':
    await removeDuplicateUsers(arg1);
    break;
  case 'messages':
    await getRoomMessages(arg1, arg2 ? parseInt(arg2) : 10);
    break;
  case 'help':
  default:
    showHelp();
    break;
}
