import React, { useState, useEffect } from 'react';
import { supabaseService } from '@/services/supabaseService';

interface SupabaseConnectionTestProps {
  onConnectionChange?: (isConnected: boolean) => void;
}

export const SupabaseConnectionTest: React.FC<SupabaseConnectionTestProps> = ({ onConnectionChange }) => {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'failed'>('testing');
  const [error, setError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  const testConnection = async () => {
    setConnectionStatus('testing');
    setError(null);
    
    try {
      const result = await supabaseService.testConnection();
      if (result.success) {
        setConnectionStatus('connected');
        onConnectionChange?.(true);
      } else {
        setConnectionStatus('failed');
        setError(result.error || 'Unknown connection error');
        onConnectionChange?.(false);
      }
    } catch (error) {
      setConnectionStatus('failed');
      setError(error instanceof Error ? error.message : 'Unknown connection error');
      onConnectionChange?.(false);
    }
  };

  const retryConnection = async () => {
    setIsRetrying(true);
    await testConnection();
    setIsRetrying(false);
  };

  useEffect(() => {
    testConnection();
  }, []);

  if (connectionStatus === 'testing') {
    return (
      <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span className="text-blue-700">Testing connection to Supabase...</span>
      </div>
    );
  }

  if (connectionStatus === 'connected') {
    return (
      <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="w-4 h-4 bg-green-500 rounded-full"></div>
        <span className="text-green-700">Connected to Supabase successfully</span>
      </div>
    );
  }

  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-center gap-2 mb-2">
        <div className="w-4 h-4 bg-red-500 rounded-full"></div>
        <span className="text-red-700 font-medium">Connection Failed</span>
      </div>
      
      {error && (
        <p className="text-red-600 text-sm mb-3">{error}</p>
      )}
      
      <div className="space-y-2 text-sm text-red-600">
        <p><strong>Possible solutions:</strong></p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>Check your internet connection</li>
          <li>Verify your Supabase project is active</li>
          <li>Check environment variables in .env.local</li>
          <li>Ensure database tables are created</li>
        </ul>
      </div>
      
      <button
        onClick={retryConnection}
        disabled={isRetrying}
        className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isRetrying ? 'Retrying...' : 'Retry Connection'}
      </button>
    </div>
  );
};
