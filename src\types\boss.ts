export interface BossDropItem {
  name: string;
  type: 'weapon' | 'armor' | 'accessory' | 'material' | 'scroll' | 'other';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  dropRate?: string; // e.g., "5%", "rare", "guaranteed"
}

export interface BossCoordinates {
  x: number;
  y: number;
  map?: string;
}

export interface Boss {
  id: string;
  location: string;
  name: string;
  level: number;
  respawnTime: number; // in hours
  respawnVariance?: number; // variance in minutes (±)
  hp?: number;
  type?: 'field' | 'dungeon' | 'raid' | 'epic'; // Made optional for backward compatibility
  difficulty?: 'easy' | 'medium' | 'hard' | 'extreme'; // Made optional for backward compatibility
  coordinates?: BossCoordinates;
  drops: BossDropItem[];
  description?: string;
  strategy?: string;
  minPlayers?: number;
  maxPlayers?: number;
  lastKilled?: Date;
  nextRespawn?: Date;
  isActive: boolean;
}

export interface TimerState {
  [bossId: string]: {
    lastKilled?: Date;
    nextRespawn?: Date;
    isActive: boolean;
    notificationSent?: boolean;
  };
}

export interface NotificationSettings {
  enabled: boolean;
  warningMinutes: number[]; // e.g., [30, 10, 5] for notifications at 30min, 10min, 5min before spawn
  sound: boolean;
  desktop: boolean;
}

// Real-time collaboration types
export interface RealTimeUser {
  id: string;
  name: string;
  color?: string;
  joinedAt: Date;
}

export interface RealTimeMessage {
  type: 'timer_start' | 'timer_stop' | 'timer_reset' | 'boss_add' | 'boss_update' | 'boss_delete' | 'user_join' | 'user_leave' | 'sync_request' | 'sync_response' | 'full_sync' | 'room_closed' | 'state_update';
  userId: string;
  userName: string;
  timestamp: Date;
  data: TimerAction | BossAction | { timerState?: TimerState; customBosses?: Boss[] } | Record<string, unknown>;
}

export interface RealTimeRoom {
  id: string;
  name: string;
  createdBy: string;
  createdAt: Date;
  users: RealTimeUser[];
  isActive: boolean;
  hasPassword: boolean;
  password?: string;
}

export interface TimerAction {
  bossId: string;
  action: 'start' | 'stop' | 'reset';
  timestamp: Date;
  killTime?: Date;
  respawnHours?: number;
}

export interface BossAction {
  action: 'add' | 'update' | 'delete';
  boss?: Boss;
  bossId?: string;
  updates?: Partial<Boss>;
  timestamp: Date;
}
