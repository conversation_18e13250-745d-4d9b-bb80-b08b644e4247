(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2015:(e,t,r)=>{"use strict";r.d(t,{default:()=>_});var a=r(5155),s=r(2115);let o="boss-notification-settings",n={enabled:!0,warningMinutes:[30,10,5],sound:!0,desktop:!0};function i(){let[e,t]=(0,s.useState)(n),[r,a]=(0,s.useState)("default"),[i,l]=(0,s.useState)(!1),[d,c]=(0,s.useState)(!1),[m,p]=(0,s.useState)(null);(0,s.useEffect)(()=>{l(!0)},[]),(0,s.useEffect)(()=>{if(!i)return;let e=localStorage.getItem(o);if(e)try{let r=JSON.parse(e);t({...n,...r})}catch(e){console.error("Failed to parse notification settings:",e)}"Notification"in window&&a(Notification.permission)},[i]),(0,s.useEffect)(()=>{i&&localStorage.setItem(o,JSON.stringify(e))},[e,i]),(0,s.useEffect)(()=>()=>{m&&clearInterval(m)},[m]);let u=(0,s.useCallback)(async()=>{if(!("Notification"in window))return console.warn("This browser does not support notifications"),!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;let e=await Notification.requestPermission();return a(e),"granted"===e},[]),h=(0,s.useCallback)((t,r)=>{if(e.enabled&&e.desktop&&"granted"===Notification.permission){let e=new Notification(t,{icon:"/favicon.ico",badge:"/favicon.ico",...r});return setTimeout(()=>{e.close()},5e3),e}},[e]),x=(0,s.useCallback)(()=>{if(!e.enabled||!e.sound)return;m&&(clearInterval(m),p(null)),c(!0);let t=()=>{try{let e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),r=e.createGain();t.connect(r),r.connect(e.destination),t.frequency.value=1e3,t.type="square",r.gain.setValueAtTime(.7,e.currentTime),r.gain.exponentialRampToValueAtTime(.01,e.currentTime+.8),t.start(e.currentTime),t.stop(e.currentTime+.8)}catch(e){console.warn("Could not play notification sound:",e)}};t();let r=setInterval(t,2e3);p(r),setTimeout(()=>{r&&(clearInterval(r),p(null),c(!1))},3e4)},[e,m]);return{settings:e,permission:r,requestPermission:u,showNotification:h,playNotificationSound:x,stopNotificationSound:(0,s.useCallback)(()=>{m&&(clearInterval(m),p(null)),c(!1)},[m]),isPlaying:d,updateSettings:(0,s.useCallback)(e=>{t(t=>({...t,...e}))},[])}}let l=(0,r(4982).UU)("https://xnoshokkykbsffdylmic.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhub3Nob2treWtic2ZmZHlsbWljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NTc4NjAsImV4cCI6MjA2NTAzMzg2MH0.8by-bmaSGoILdqVCCS1TpNDVzrseeGy5UBeJUPRfPqE",{realtime:{params:{eventsPerSecond:10}}}),d=e=>btoa(e+"l2m-salt"),c=(e,t)=>d(e)===t;class m{async testConnection(){try{console.log("Testing Supabase connection..."),console.log("Supabase URL:","https://xnoshokkykbsffdylmic.supabase.co"),console.log("Supabase Key exists:",!0);let{error:e}=await l.from("rooms").select("count").limit(1);if(e)return console.error("Supabase connection test failed:",e),{success:!1,error:e.message};return console.log("Supabase connection test successful"),{success:!0}}catch(e){return console.error("Supabase connection test error:",e),{success:!1,error:e instanceof Error?e.message:"Unknown connection error"}}}async createRoom(e){let{data:t,error:r}=await l.from("rooms").insert({id:e.id,host_id:e.host_id,host_name:e.host_name,password_hash:e.password?d(e.password):null,has_password:!!e.password,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),last_activity:new Date().toISOString()}).select().single();if(r)throw console.error("Error creating room:",r),Error("Failed to create room: ".concat(r.message));return{id:t.id,host_id:t.host_id,host_name:t.host_name,password_hash:t.password_hash||void 0,has_password:t.has_password,is_active:t.is_active,created_at:t.created_at,updated_at:t.updated_at,last_activity:t.last_activity}}async getRoomData(e){try{let{data:t,error:r}=await l.from("rooms").select("*").eq("id",e).eq("is_active",!0).single();if(r){if("PGRST116"===r.code)return null;if(console.error("Error getting room data:",r),r.message.includes("NetworkError")||r.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw Error("Failed to get room data: ".concat(r.message))}return{id:t.id,host_id:t.host_id,host_name:t.host_name,password_hash:t.password_hash||void 0,has_password:t.has_password,is_active:t.is_active,created_at:t.created_at,updated_at:t.updated_at,last_activity:t.last_activity}}catch(e){if(console.error("Error in getRoomData:",e),e instanceof Error){if(e.message.includes("NetworkError")||e.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw e}throw Error("Failed to get room data: Unknown error")}}async verifyRoomPassword(e,t){let r=await this.getRoomData(e);return r&&r.has_password&&r.password_hash?c(t,r.password_hash):!(null==r?void 0:r.has_password)}async addUserToRoom(e,t){let{data:r}=await l.from("room_users").select("id").eq("room_id",e).eq("user_id",t.id).single();if(r){let{error:r}=await l.from("room_users").update({user_name:t.name,last_seen:new Date().toISOString(),is_online:!0}).eq("room_id",e).eq("user_id",t.id);if(r)throw console.error("Error updating user in room:",r),Error("Failed to update user in room: ".concat(r.message))}else{let{error:r}=await l.from("room_users").insert({room_id:e,user_id:t.id,user_name:t.name,joined_at:new Date().toISOString(),last_seen:new Date().toISOString(),is_online:!0});if(r)throw console.error("Error adding user to room:",r),Error("Failed to add user to room: ".concat(r.message))}await this.updateRoomActivity(e)}async updateUserHeartbeat(e,t){try{let{error:r}=await l.from("room_users").update({last_seen:new Date().toISOString(),is_online:!0}).eq("room_id",e).eq("user_id",t);if(r){if(console.error("Error updating user heartbeat:",r),r.message.includes("NetworkError")||r.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw Error("Failed to update user heartbeat: ".concat(r.message))}await this.updateRoomActivity(e)}catch(e){if(console.error("Error in updateUserHeartbeat:",e),e instanceof Error){if(e.message.includes("NetworkError")||e.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw e}throw Error("Failed to update user heartbeat: Unknown error")}}async removeUserFromRoom(e,t){let{error:r}=await l.from("room_users").update({is_online:!1,last_seen:new Date().toISOString()}).eq("room_id",e).eq("user_id",t);if(r)throw console.error("Error removing user from room:",r),Error("Failed to remove user from room: ".concat(r.message));await this.updateRoomActivity(e)}async getRoomUsers(e){let{data:t,error:r}=await l.from("room_users").select("*").eq("room_id",e).eq("is_online",!0).order("joined_at",{ascending:!0});if(r)throw console.error("Error getting room users:",r),Error("Failed to get room users: ".concat(r.message));return t.map(e=>({id:e.user_id,name:e.user_name,joinedAt:new Date(e.joined_at)}))}async sendMessage(e,t){let{error:r}=await l.from("room_messages").insert({room_id:e,user_id:t.userId,user_name:t.userName,message_type:t.type,message_data:t.data,created_at:new Date().toISOString()});if(r)throw console.error("Error sending message:",r),Error("Failed to send message: ".concat(r.message));await this.updateRoomActivity(e)}async getMessagesSince(e,t){let{data:r,error:a}=await l.from("room_messages").select("*").eq("room_id",e).gt("created_at",t).order("created_at",{ascending:!0});if(a)throw console.error("Error getting messages:",a),Error("Failed to get messages: ".concat(a.message));return r}async saveRoomState(e,t,r,a){try{let{error:s}=await l.from("room_state").upsert({room_id:e,state_type:t,state_data:r,updated_at:new Date().toISOString(),updated_by:a},{onConflict:"room_id,state_type",ignoreDuplicates:!1});if(s)throw console.error("Error saving room state:",s),Error("Failed to save room state: ".concat(s.message))}catch(e){if(console.error("Error in saveRoomState:",e),e instanceof Error){if(e.message.includes("NetworkError")||e.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw e}throw Error("Failed to save room state: Unknown error")}}async getRoomState(e,t){try{let r=l.from("room_state").select("*").eq("room_id",e);t&&(r=r.eq("state_type",t));let{data:a,error:s}=await r.order("updated_at",{ascending:!1});if(s)throw console.error("Error getting room state:",s),Error("Failed to get room state: ".concat(s.message));return a||[]}catch(e){if(console.error("Error in getRoomState:",e),e instanceof Error){if(e.message.includes("NetworkError")||e.message.includes("fetch"))throw Error("Network connection failed. Please check your internet connection and try again.");throw e}throw Error("Failed to get room state: Unknown error")}}async updateRoomActivity(e){try{let{error:t}=await l.from("rooms").update({last_activity:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",e);t&&(console.error("Error updating room activity:",t),(t.message.includes("NetworkError")||t.message.includes("fetch"))&&console.warn("Network connection issue while updating room activity - will retry later"))}catch(e){console.error("Error in updateRoomActivity:",e)}}async closeRoom(e,t){let{error:r}=await l.from("rooms").update({is_active:!1,updated_at:new Date().toISOString()}).eq("id",e).eq("host_id",t);if(r)throw console.error("Error closing room:",r),Error("Failed to close room: ".concat(r.message));await l.from("room_users").update({is_online:!1,last_seen:new Date().toISOString()}).eq("room_id",e)}subscribeToRoom(e,t){let r=l.channel("room-users-".concat(e)).on("postgres_changes",{event:"*",schema:"public",table:"room_users",filter:"room_id=eq.".concat(e)},e=>{var r,a;"INSERT"===e.eventType&&e.new.is_online?null==(r=t.onUserJoin)||r.call(t,{id:e.new.user_id,name:e.new.user_name,joinedAt:new Date(e.new.joined_at)}):"UPDATE"!==e.eventType||e.new.is_online||null==(a=t.onUserLeave)||a.call(t,e.new.user_id)}).subscribe(),a=l.channel("room-messages-".concat(e)).on("postgres_changes",{event:"INSERT",schema:"public",table:"room_messages",filter:"room_id=eq.".concat(e)},e=>{var r;null==(r=t.onMessage)||r.call(t,{type:e.new.message_type,userId:e.new.user_id,userName:e.new.user_name,timestamp:new Date(e.new.created_at),data:e.new.message_data})}).subscribe();return()=>{r.unsubscribe(),a.unsubscribe()}}}let p=new m,u=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#06B6D4","#84CC16"];function h(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!1),[o,n]=(0,s.useState)(null),[i,l]=(0,s.useState)(null),[d,c]=(0,s.useState)([]),[m,h]=(0,s.useState)("disconnected"),[x,y]=(0,s.useState)(null),g=(0,s.useRef)(null),b=(0,s.useRef)([]);(0,s.useRef)(new Date().toISOString());let w=(0,s.useRef)(null),f=(0,s.useCallback)(()=>Math.random().toString(36).substring(2,8).toUpperCase(),[]),v=(0,s.useCallback)(e=>({id:Math.random().toString(36).substring(2,15),name:e,color:u[Math.floor(Math.random()*u.length)],joinedAt:new Date}),[]),k=(0,s.useCallback)(e=>(b.current.push(e),()=>{b.current=b.current.filter(t=>t!==e)}),[]),j=(0,s.useCallback)(async e=>{if(o)try{console.log("Broadcasting message:",e.type,"from:",e.userName),await p.sendMessage(o,e)}catch(e){console.error("Failed to broadcast message:",e),y("Failed to send message")}},[o]),N=(0,s.useCallback)(e=>{g.current&&g.current(),g.current=p.subscribeToRoom(e,{onUserJoin:e=>{console.log("User joined:",e.name),c(t=>t.find(t=>t.id===e.id)?t:[...t,e])},onUserLeave:e=>{console.log("User left:",e),c(t=>t.filter(t=>t.id!==e))},onMessage:e=>{e.userId!==(null==i?void 0:i.id)&&(console.log("Received message:",e.type,"from:",e.userName),b.current.forEach(t=>t(e)))}})},[null==i?void 0:i.id]),R=(0,s.useCallback)(async()=>{if(o&&i)try{await p.updateUserHeartbeat(o,i.id)}catch(e){console.error("Failed to update heartbeat:",e),e instanceof Error&&e.message.includes("Network connection failed")&&(h("connecting"),y("Connection issues detected. Attempting to reconnect..."),setTimeout(async()=>{try{let e=await p.testConnection();e.success?(h("connected"),y(null)):y("Connection failed: ".concat(e.error))}catch(e){console.error("Connection test failed:",e),y("Unable to connect to the server. Please check your internet connection.")}},5e3))}},[o,i]),S=(0,s.useCallback)(async(e,r)=>{try{console.log("Creating room for user:",e,"with password:",!!r),h("connecting"),y(null),console.log("Testing Supabase connection...");let s=await p.testConnection();if(!s.success)throw Error("Connection failed: ".concat(s.error));console.log("Connection test successful");let o=f(),i=v(e);return console.log("Generated room ID:",o,"User:",i),await p.createRoom({id:o,host_id:i.id,host_name:i.name,password:r}),await p.addUserToRoom(o,i),n(o),l(i),a(!0),c([i]),t(!0),h("connected"),localStorage.setItem("realtime-room",JSON.stringify({roomId:o,user:i,isHost:!0})),N(o),w.current&&clearInterval(w.current),w.current=setInterval(R,3e4),console.log("Room created successfully:",o),o}catch(e){throw console.error("Failed to create room:",e),y(e instanceof Error?e.message:"Failed to create room"),h("disconnected"),e}},[f,v,N,R]),C=(0,s.useCallback)(async(e,r,s)=>{try{console.log("Joining room:",e,"as user:",r,"with password:",!!s),h("connecting"),y(null),console.log("Testing Supabase connection...");let o=await p.testConnection();if(!o.success)throw Error("Connection failed: ".concat(o.error));console.log("Connection test successful");let i=await p.getRoomData(e);if(!i){y("Room not found. Please check the room code and make sure the host has created the room."),h("disconnected");return}if(!i.is_active){y("This room has been closed by the host."),h("disconnected");return}if(i.has_password){if(!s){y("This room requires a password."),h("disconnected");return}if(!await p.verifyRoomPassword(e,s)){y("Incorrect password."),h("disconnected");return}}let d=v(r);console.log("Generated user for joining:",d),await p.addUserToRoom(e,d);let m=await p.getRoomUsers(e);n(e),l(d),a(!1),c(m),t(!0),h("connected"),localStorage.setItem("realtime-room",JSON.stringify({roomId:e,user:d,isHost:!1})),N(e),w.current&&clearInterval(w.current),w.current=setInterval(R,3e4);let u={type:"user_join",userId:d.id,userName:d.name,timestamp:new Date,data:{}};await j(u),console.log("Successfully joined room:",e)}catch(e){console.error("Failed to join room:",e),y(e instanceof Error?e.message:"Failed to join room"),h("disconnected")}},[v,N,R,j]),L=(0,s.useCallback)(async()=>{if(i){let e={type:"user_leave",userId:i.id,userName:i.name,timestamp:new Date,data:{}};await j(e)}if(o&&i)try{await p.removeUserFromRoom(o,i.id)}catch(e){console.error("Failed to remove user from room:",e)}g.current&&(g.current(),g.current=null),w.current&&(clearInterval(w.current),w.current=null),t(!1),a(!1),n(null),l(null),c([]),h("disconnected"),y(null),localStorage.removeItem("realtime-room")},[i,o,j]),B=(0,s.useCallback)(async()=>{if(r&&o&&i)try{let e={type:"room_closed",userId:i.id,userName:i.name,timestamp:new Date,data:{reason:"Host closed the room"}};await j(e),await p.closeRoom(o,i.id),await L()}catch(e){console.error("Failed to close room:",e)}},[r,o,i,j,L]),E=(0,s.useCallback)((e,t)=>{i&&j({type:e,userId:i.id,userName:i.name,timestamp:new Date,data:t})},[i,j]),A=(0,s.useCallback)(async(e,t)=>{if(o&&i)try{await p.saveRoomState(o,e,t,i.id)}catch(e){console.error("Failed to save room state:",e)}},[o,i]),_=(0,s.useCallback)(async e=>{if(!o)return[];try{return await p.getRoomState(o,e)}catch(e){return console.error("Failed to get room state:",e),[]}},[o]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("realtime-room");if(e)try{let{roomId:r,user:s,isHost:o}=JSON.parse(e);p.getRoomData(r).then(e=>{e&&e.is_active?(n(r),l(s),a(o),t(!0),h("connected"),N(r),w.current=setInterval(R,3e4),p.getRoomUsers(r).then(e=>{c(e)}).catch(e=>{console.error("Failed to get room users during restoration:",e)})):localStorage.removeItem("realtime-room")}).catch(e=>{console.error("Failed to restore room:",e),e instanceof Error&&e.message.includes("Network connection failed")&&(y("Connection issues detected. Please check your internet connection."),h("disconnected")),localStorage.removeItem("realtime-room")})}catch(e){console.error("Failed to restore room from localStorage:",e),localStorage.removeItem("realtime-room")}},[N,R]),(0,s.useEffect)(()=>()=>{g.current&&g.current(),w.current&&clearInterval(w.current)},[]),{isConnected:e,isHost:r,roomId:o,currentUser:i,connectedUsers:d,connectionStatus:m,error:x,createRoom:S,joinRoom:C,leaveRoom:L,closeRoom:B,sendMessage:E,addMessageHandler:k,saveRoomState:A,getRoomState:_}}let x="boss-timer-state",y=[{id:"chertuba-barracks",location:"Chertuba's Barracks",name:"Chertuba",level:40,respawnTime:6,hp:85e4,minPlayers:3,maxPlayers:8,coordinates:{x:120,y:85},drops:[{name:"Chertuba's Soul Necklace",type:"accessory",rarity:"rare",dropRate:"Low"},{name:"Tsurugi",type:"weapon",rarity:"rare",dropRate:"Low"},{name:"Caliburs Dual Blades",type:"weapon",rarity:"rare",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful orc commander guarding the barracks entrance.",strategy:"Focus on crowd control and maintain distance from his charge attack.",isActive:!1},{id:"chertuba-barracks-awakened",location:"Chertuba's Barracks",name:"Awakened Chertuba",level:55,respawnTime:6,hp:12e5,minPlayers:5,maxPlayers:8,coordinates:{x:120,y:85},drops:[{name:"Guardian's Sword",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystal Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Apella's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Chertuba Tsurugi",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Caliburs Dual Blades",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Body Slasher",type:"weapon",rarity:"rare",dropRate:"Medium"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The awakened form of Chertuba with enhanced abilities.",strategy:"Requires coordinated team play and strong healing support.",isActive:!1},{id:"southern-wasteland",location:"Southern Wasteland",name:"Basila",level:40,respawnTime:4,hp:65e4,minPlayers:2,maxPlayers:6,coordinates:{x:200,y:150},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A fierce beast roaming the wasteland.",strategy:"Kite around rocks to avoid its pounce attack.",isActive:!1},{id:"ruins-of-despair",location:"Ruins of Despair",name:"Kelsus",level:40,respawnTime:10,hp:75e4,minPlayers:3,maxPlayers:7,coordinates:{x:180,y:220},drops:[{name:"Cursed Weapon Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Cursed Armor Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An ancient guardian of the ruined temple.",strategy:"Interrupt his channeling abilities to prevent massive damage.",isActive:!1},{id:"ant-nest-b2",location:"Ant Nest (B2)",name:"Savan",level:45,respawnTime:12,hp:11e5,minPlayers:5,maxPlayers:8,coordinates:{x:250,y:200},drops:[{name:"Savan's Robe",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Necklace of Immortality",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Desperion's Staff",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Cursed Weapon Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful ant queen deep in the nest.",strategy:"Clear adds first, then focus on the queen.",isActive:!1},{id:"ant-nest-b3",location:"Ant Nest (B3)",name:"Queen Ant",level:50,respawnTime:6,hp:2e6,minPlayers:6,maxPlayers:12,coordinates:{x:75,y:200},drops:[{name:"Queen Ant's Wings",type:"material",rarity:"legendary",dropRate:"Very Low"},{name:"Majestic Robe",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Majestic Keshanberk",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Ring of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The massive queen of the ant colony with devastating area attacks.",strategy:"Requires raid coordination. Focus on adds management and positioning.",isActive:!1},{id:"bloodstained-swampland",location:"Bloodstained Swampland",name:"Tromba",level:60,respawnTime:7,hp:15e5,minPlayers:4,maxPlayers:8,coordinates:{x:95,y:180},drops:[{name:"Damascus Dual Blades",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Demon's Orb",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blue Wolf Helmet",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive swamp creature with toxic abilities.",strategy:"Bring poison resistance and focus on mobility.",isActive:!1},{id:"bee-hive",location:"Bee Hive",name:"Felis",level:40,respawnTime:3,hp:5e5,minPlayers:2,maxPlayers:5,coordinates:{x:160,y:90},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Ultimate Evasion)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Concentration)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A swift feline predator with high mobility.",strategy:"Quick fight, but watch for his leap attacks.",isActive:!1},{id:"rebel-territory",location:"Rebel Territory",name:"Talakin",level:40,respawnTime:10,hp:8e5,minPlayers:3,maxPlayers:7,coordinates:{x:140,y:170},drops:[{name:"Full Plate Armor",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Full Plate Gauntlets",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Sword of Nightmares",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystal Dagger",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Crystallized Ice Bow",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Soul Separator",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"Leader of the rebel forces with tactical combat skills.",strategy:"Disrupt his formations and focus fire to prevent reinforcements.",isActive:!1},{id:"dion-plains",location:"Dion Plains",name:"Enkura",level:40,respawnTime:6,hp:6e5,minPlayers:2,maxPlayers:6,coordinates:{x:220,y:130},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"Medium"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Defense Aura)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Sonic Blaster)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A territorial beast protecting the plains.",strategy:"Straightforward fight, maintain distance from charge attacks.",isActive:!1},{id:"dion-hills",location:"Dion Hills",name:"Pan Dra'eed",level:40,respawnTime:12,hp:9e5,minPlayers:4,maxPlayers:8,coordinates:{x:240,y:110},drops:[{name:"Pan Dra'eed's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Drake Leather Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Energize)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Cancellation)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An ancient mage with powerful elemental magic.",strategy:"Interrupt his spell casting and use magic resistance.",isActive:!1},{id:"cruma-marshlands",location:"Cruma Marshlands",name:"Mutated Cruma",level:50,respawnTime:8,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:300,y:200},drops:[{name:"Cruma's Horn",type:"material",rarity:"epic",dropRate:"Low"},{name:"Cruma's Shell",type:"material",rarity:"epic",dropRate:"Low"},{name:"Ring of Passion",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Damascus Dual Blades",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A mutated creature with enhanced abilities.",strategy:"Focus on positioning and avoid its toxic attacks.",isActive:!1},{id:"morgue",location:"Morgue",name:"Tempest",level:45,respawnTime:6,hp:95e4,minPlayers:3,maxPlayers:7,coordinates:{x:110,y:60},drops:[{name:"Demon's Orb",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Devil's Pact",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Vilesoil Valefar Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Touch of Life",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"An undead warrior with lightning-fast attacks.",strategy:"Use holy magic and maintain high mobility.",isActive:!1},{id:"cruma-tower-b4",location:"Cruma Tower (B4)",name:"Contaminated Cruma",level:45,respawnTime:8,hp:1e6,minPlayers:4,maxPlayers:8,coordinates:{x:320,y:180},drops:[{name:"Spear Training (Absolute Spear)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Cloak of Authority",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Tiat's Belt",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Resist",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Greatsword Techniques (Reflect Sun)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Elemental Spike)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"A contaminated creature with enhanced toxic abilities.",strategy:"Requires strong poison resistance and coordinated attacks.",isActive:!1},{id:"cruma-tower-b6",location:"Cruma Tower (B6)",name:"Katan",level:55,respawnTime:10,hp:14e5,minPlayers:5,maxPlayers:8,coordinates:{x:340,y:160},drops:[{name:"Feather Eye Buster",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Art of Dual Blades (Breaking Armor)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Boots of Moonsouls Cloak",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Baium's Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Inheritor's Book (Increase Dual Blades)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Sonic Mastery",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Crossbow Guidebook (Chain Strike)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blessing",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Blue Wolf Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Inferno Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Belt of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"}],description:"A powerful guardian of the tower depths.",strategy:"High-level coordination required, focus on interrupting abilities.",isActive:!1},{id:"cruma-tower-b7",location:"Cruma Tower (B7)",name:"Core Susceptor",level:60,respawnTime:10,hp:16e5,minPlayers:6,maxPlayers:8,coordinates:{x:360,y:140},drops:[{name:"Sarunga",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Lord's Authority",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Susceptor's Heart",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Core Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Dark Crystal Helmet",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"}],description:"The core guardian of Cruma Tower with immense power.",strategy:"Requires maximum coordination and high-level equipment.",isActive:!1},{id:"delu-dwellings",location:"Delu Dwellings",name:"Sarka",level:45,respawnTime:10,hp:85e4,minPlayers:3,maxPlayers:7,coordinates:{x:190,y:250},drops:[{name:"Full Plate Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Full Plate Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A tribal shaman with nature-based magic.",strategy:"Dispel his buffs and avoid standing in nature traps.",isActive:!1},{id:"floran-fields",location:"Floran Fields",name:"Timitris",level:40,respawnTime:8,hp:7e5,minPlayers:3,maxPlayers:6,coordinates:{x:260,y:140},drops:[{name:"Blue Wolf Breastplate",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blue Wolf Gloves",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Detect Weakness)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Detect Weakness)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A plant-like creature that controls the field vegetation.",strategy:"Use fire magic and clear the area of plant minions first.",isActive:!1},{id:"giants-vestige",location:"Giants' Vestige",name:"Stonegeist",level:45,respawnTime:7,hp:1e6,minPlayers:4,maxPlayers:8,coordinates:{x:300,y:80},drops:[{name:"Demon's Dagger",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Stakato Queen's Staff",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blood Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Venom)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Rapid Shot)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Shadow Blade)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive stone golem left by ancient giants.",strategy:"Focus on weak points and use earth-shattering abilities.",isActive:!1},{id:"tanor-canyon",location:"Tanor Canyon",name:"Gahareth",level:70,respawnTime:9,hp:18e5,minPlayers:6,maxPlayers:8,coordinates:{x:400,y:120},drops:[{name:"Dark Legion's Edge",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Majestic Ring",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Bracelet of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Belt of Blessing",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Mana Seeker",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A powerful demon lord commanding the canyon.",strategy:"Requires high-level coordination and strong defensive abilities.",isActive:!1},{id:"medusa-garden",location:"Medusa Garden",name:"Medusa",level:55,respawnTime:10,hp:13e5,minPlayers:5,maxPlayers:8,coordinates:{x:380,y:100},drops:[{name:"Medusa's Helm",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Medusa's Cloak",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Themo Tongue",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Arcane Shield)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Triple Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"The legendary Medusa with petrifying gaze.",strategy:"Avoid direct eye contact and use ranged attacks.",isActive:!1},{id:"death-pass",location:"Death Pass",name:"Black Lily",level:65,respawnTime:12,hp:17e5,minPlayers:6,maxPlayers:8,coordinates:{x:420,y:80},drops:[{name:"Cabrio's Hand",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Demon's Boots",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Black Lily's Magic Necklace",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Accessory Enchant Scroll",type:"scroll",rarity:"rare",dropRate:"Medium"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Judgment)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Restore Casting)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Priest's Records (Judgment)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Textbook of Magic (Critical Magic)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Assassination Bible (Focus Accuracy)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A dark sorceress with deadly magic.",strategy:"High magic resistance required, interrupt her casting.",isActive:!1},{id:"pillagers-outpost",location:"Pillagers' Outpost",name:"Matura",level:50,respawnTime:6,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:450,y:150},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Assassination Bible (Deadly Blow)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Double Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"Leader of the pillager forces.",strategy:"Fast-paced combat, focus on mobility.",isActive:!1},{id:"brekas-stronghold",location:"Breka's Stronghold",name:"Breka",level:50,respawnTime:6,hp:12e5,minPlayers:4,maxPlayers:8,coordinates:{x:470,y:130},drops:[{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Battle Tome (Deflect Arrow)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Art of Dual Blades (Double Slash)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A fortified stronghold commander.",strategy:"Siege-style combat, coordinate attacks on weak points.",isActive:!1},{id:"gorgon-flower-garden",location:"Gorgon Flower Garden",name:"Pan Marrod",level:50,respawnTime:5,hp:11e5,minPlayers:4,maxPlayers:8,coordinates:{x:500,y:180},drops:[{name:"Akai Longbow",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Ultimate Defense)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A nature guardian with plant-based abilities.",strategy:"Use fire attacks and clear minions quickly.",isActive:!1},{id:"dragon-valley-north",location:"Dragon Valley (North)",name:"Behemoth",level:65,respawnTime:9,hp:16e5,minPlayers:6,maxPlayers:8,coordinates:{x:520,y:200},drops:[{name:"Sword of Miracles",type:"weapon",rarity:"epic",dropRate:"Low"},{name:"Gaiters",type:"armor",rarity:"epic",dropRate:"Low"},{name:"Behemoth Leather Belt",type:"accessory",rarity:"epic",dropRate:"Low"},{name:"Blessed Weapon Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Armor Enchant Scroll",type:"scroll",rarity:"common",dropRate:"High"},{name:"Blessed Life Stone",type:"material",rarity:"common",dropRate:"High"},{name:"Epic Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Unique Craft Recipe",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Magic Lizard Storm",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Battle Tome (Double Shock)",type:"other",rarity:"epic",dropRate:"Very Low"},{name:"Archery Manual (Scooter Stance)",type:"other",rarity:"epic",dropRate:"Very Low"}],description:"A massive dragon-like creature with devastating attacks.",strategy:"Requires maximum coordination and dragon-slaying equipment.",isActive:!1}],g="custom-bosses",b={common:"text-gray-600 bg-gray-100 dark:text-gray-300 dark:bg-gray-700",rare:"text-blue-600 bg-blue-100 dark:text-blue-300 dark:bg-blue-900",epic:"text-purple-600 bg-purple-100 dark:text-purple-300 dark:bg-purple-900",legendary:"text-yellow-600 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900"};function w(e){let{drop:t}=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:t.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:t.type})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(b[t.rarity]),children:t.rarity}),t.dropRate&&(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t.dropRate})]})]})}function f(e){var t;let{boss:r,isOpen:s,onClose:o}=e;return s&&r?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75",onClick:o}),(0,a.jsxs)("div",{className:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:r.name}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.location})]}),(0,a.jsx)("button",{onClick:o,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Stats"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Level:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:r.level})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"HP:"}),(0,a.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:(null==(t=r.hp)?void 0:t.toLocaleString())||"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Respawn:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:[r.respawnTime,"h"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Players:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium text-gray-900 dark:text-white",children:[r.minPlayers,"-",r.maxPlayers]})]})]})]}),r.coordinates&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Location"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Coordinates: (",r.coordinates.x,", ",r.coordinates.y,")",r.coordinates.map&&" - ".concat(r.coordinates.map)]})]}),r.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.description})]}),r.strategy&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Strategy"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r.strategy})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Drops"}),(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:r.drops.map((e,t)=>(0,a.jsx)(w,{drop:e},"".concat(e.name,"-").concat(e.type,"-").concat(t)))})]})]})]})]})}):null}function v(e){let{isOpen:t,onClose:r}=e,{settings:o,permission:n,requestPermission:l,updateSettings:d}=i(),[c,m]=(0,s.useState)("");if(!t)return null;let p=e=>{d({warningMinutes:o.warningMinutes.filter(t=>t!==e)})},u=async()=>{await l()};return(0,a.jsxs)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white shadow-2xl rounded-2xl border-2 border-gray-300 p-6",style:{backgroundColor:"#ffffff",zIndex:1e4},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6 border-b border-gray-200 pb-4",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:"Notification Settings"}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-full hover:bg-gray-100",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Browser Permissions"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Status: ",(0,a.jsx)("span",{className:"font-medium ".concat("granted"===n?"text-green-600":"denied"===n?"text-red-600":"text-yellow-600"),children:"granted"===n?"Allowed":"denied"===n?"Denied":"Not requested"})]})}),"granted"!==n&&(0,a.jsx)("button",{onClick:u,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors",children:"Request Permission"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Enable Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive notifications for boss respawns"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.enabled,onChange:e=>d({enabled:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Sound Alerts"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Play sound when notifications appear"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.sound,onChange:e=>d({sound:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Desktop Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Show desktop notification popups"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.desktop,onChange:e=>d({desktop:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Warning Times"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Get notified X minutes before boss respawn"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:o.warningMinutes.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200",children:[e," min",(0,a.jsx)("button",{onClick:()=>p(e),className:"ml-2 text-blue-600 hover:text-blue-800 font-bold",children:"\xd7"})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"number",value:c,onChange:e=>m(e.target.value),placeholder:"Minutes",min:"1",max:"1440",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("button",{onClick:()=>{let e=parseInt(c);e>0&&!o.warningMinutes.includes(e)&&(d({warningMinutes:[...o.warningMinutes,e].sort((e,t)=>t-e)}),m(""))},disabled:!c||0>=parseInt(c),className:"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors font-medium",children:"Add"})]})]})]})]})]})}function k(e){let{isOpen:t,onClose:r,onAddBoss:o}=e,[n,i]=(0,s.useState)({name:"",location:"",level:1,respawnTime:1}),l=(e,t)=>{i(r=>({...r,[e]:t}))},d=()=>{i({name:"",location:"",level:1,respawnTime:1})};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Add New Boss"}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!n.name.trim()||!n.location.trim())return void alert("Please fill in the boss name and location.");o({id:"custom-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),name:n.name.trim(),location:n.location.trim(),level:n.level,respawnTime:n.respawnTime,hp:1e5,type:"field",difficulty:"medium",minPlayers:1,maxPlayers:8,drops:[],isActive:!1}),d(),r()},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Boss Name *"}),(0,a.jsx)("input",{type:"text",value:n.name,onChange:e=>l("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter boss name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Location *"}),(0,a.jsx)("input",{type:"text",value:n.location,onChange:e=>l("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter location",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Level"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:n.level,onChange:e=>l("level",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Respawn Time (hours)"}),(0,a.jsx)("input",{type:"number",min:"0.5",step:"0.5",value:n.respawnTime,onChange:e=>l("respawnTime",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:d,className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors",children:"Reset"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors",children:"Add Boss"})]})]})]})]}):null}function j(e){let{isOpen:t,onClose:r,onUpdateBoss:o,boss:n}=e,[i,l]=(0,s.useState)({name:"",location:"",level:1,respawnTime:1});(0,s.useEffect)(()=>{n&&l({name:n.name,location:n.location,level:n.level,respawnTime:n.respawnTime})},[n]);let d=(e,t)=>{l(r=>({...r,[e]:t}))};return t&&n?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:r}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["Edit Boss: ",n.name]}),(0,a.jsx)("button",{onClick:r,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!i.name.trim()||!i.location.trim())return void alert("Please fill in the boss name and location.");n&&o({...n,name:i.name.trim(),location:i.location.trim(),level:i.level,respawnTime:i.respawnTime})},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Boss Name *"}),(0,a.jsx)("input",{type:"text",value:i.name,onChange:e=>d("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter boss name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Location *"}),(0,a.jsx)("input",{type:"text",value:i.location,onChange:e=>d("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Enter location",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Level"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:i.level,onChange:e=>d("level",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Respawn Time (hours)"}),(0,a.jsx)("input",{type:"number",min:"0.5",step:"0.5",value:i.respawnTime,onChange:e=>d("respawnTime",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:()=>{n&&l({name:n.name,location:n.location,level:n.level,respawnTime:n.respawnTime})},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors",children:"Reset"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors",children:"Update Boss"})]})]})]})]}):null}let N=(e,t)=>btoa(btoa(e)+"|"+btoa(t)),R=(e,t)=>{try{let[r,a]=atob(e).split("|"),s=btoa(t);if(a!==s)return null;return atob(r)}catch(e){return null}};function S(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!1),[o,n]=(0,s.useState)(null),[i,l]=(0,s.useState)(null),d=(0,s.useCallback)((e,t,r)=>{let a={};return Object.entries(t).forEach(e=>{var t,r;let[s,o]=e;a[s]={isActive:o.isActive,lastKilled:null==(t=o.lastKilled)?void 0:t.toISOString(),nextRespawn:null==(r=o.nextRespawn)?void 0:r.toISOString()}}),{customBosses:e,timerStates:a,sharedAt:new Date().toISOString(),sharedBy:r}},[]),c=(0,s.useCallback)(async(e,r,a,s)=>{t(!0),l(null);try{let t=d(e,r,s),o=JSON.stringify({data:t,hasPassword:!!a});a&&(o=N(o,a));let i=btoa(o),l=window.location.origin+window.location.pathname,c="".concat(l,"?share=").concat(i);return n(c),c}catch(t){let e="Failed to create share link";throw l(e),Error(e)}finally{t(!1)}},[d]),m=(0,s.useCallback)((e,t)=>{a(!0),l(null);try{let r,a=atob(e);try{r=JSON.parse(a)}catch(s){if(!t)return l("This shared data requires a password"),null;let e=R(a,t);if(!e)return l("Invalid password"),null;r=JSON.parse(e)}if(r.hasPassword&&!t)return l("This shared data requires a password"),null;let s={};return Object.entries(r.data.timerStates).forEach(e=>{let[t,r]=e;s[t]={isActive:r.isActive,lastKilled:r.lastKilled?new Date(r.lastKilled):void 0,nextRespawn:r.nextRespawn?new Date(r.nextRespawn):void 0}}),{customBosses:r.data.customBosses,timerStates:s,sharedAt:r.data.sharedAt,sharedBy:r.data.sharedBy}}catch(e){return l("Invalid share code or corrupted data"),null}finally{a(!1)}},[]),p=(0,s.useCallback)(async e=>{try{return await navigator.clipboard.writeText(e),!0}catch(r){let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),document.body.removeChild(t),!0}catch(e){return document.body.removeChild(t),!1}}},[]);return{isSharing:e,isImporting:r,shareUrl:o,error:i,createShareLink:c,parseShareData:m,copyToClipboard:p,clearError:(0,s.useCallback)(()=>{l(null)},[]),clearShareUrl:(0,s.useCallback)(()=>{n(null)},[])}}function C(e){let{isOpen:t,onClose:r,customBosses:o,timerStates:n}=e,[i,l]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),[m,p]=(0,s.useState)(!1),[u,h]=(0,s.useState)(!1),{isSharing:x,shareUrl:y,error:g,createShareLink:b,copyToClipboard:w,clearError:f,clearShareUrl:v}=S(),k=async()=>{f();try{let e=m?i:void 0,t=await b(o,n,e,d||void 0);console.log("Share URL created:",t)}catch(e){console.error("Failed to create share link:",e)}},j=async()=>{y&&await w(y)&&(h(!0),setTimeout(()=>h(!1),2e3))},N=()=>{v(),f(),l(""),c(""),p(!1),h(!1),r()};if(!t)return null;let R={activeBosses:Object.values(n).filter(e=>e.isActive).length,totalBosses:o.length+Object.keys(n).length,customBosses:o.length};return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:N}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Share Boss Timers"}),(0,a.jsx)("button",{onClick:N,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"What will be shared:"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:R.totalBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:R.activeBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:R.customBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Custom Bosses"})]})]})]}),y?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"font-medium",children:"Share link created successfully!"})]}),(0,a.jsxs)("p",{className:"text-sm",children:["Share this link with others to let them import your boss timers and custom bosses.",m&&" They will need the password you set."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Share Link"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",value:y,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),(0,a.jsx)("button",{onClick:j,className:"px-4 py-2 rounded-md transition-colors text-sm ".concat(u?"bg-green-600 text-white":"bg-gray-600 hover:bg-gray-700 text-white"),children:u?"Copied!":"Copy"})]})]}),m&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password (share this separately)"}),(0,a.jsx)("div",{className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono",children:i})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{v(),l(""),p(!1)},className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Create New Link"}),(0,a.jsx)("button",{onClick:N,className:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors",children:"Done"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Your Name (Optional)"}),(0,a.jsx)("input",{type:"text",value:d,onChange:e=>c(e.target.value),placeholder:"Enter your name or guild",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"usePassword",checked:m,onChange:e=>p(e.target.checked),className:"mr-2"}),(0,a.jsx)("label",{htmlFor:"usePassword",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password protect this share"})]}),m&&(0,a.jsx)("input",{type:"password",value:i,onChange:e=>l(e.target.value),placeholder:"Enter password",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),g&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded",children:g}),(0,a.jsx)("button",{onClick:k,disabled:x||m&&!i.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Creating Share Link..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"Create Share Link"]})})]})]})]})}function L(e){let{isOpen:t,onClose:r,onImport:o}=e,[n,i]=(0,s.useState)(""),[l,d]=(0,s.useState)(""),[c,m]=(0,s.useState)("merge"),[p,u]=(0,s.useState)(null),{isImporting:h,error:x,parseShareData:y,clearError:g}=S(),b=()=>{i(""),d(""),m("merge"),u(null),g(),r()};if(!t)return null;let w=p?{activeBosses:Object.values(p.timerStates).filter(e=>e.isActive).length,totalBosses:p.customBosses.length+Object.keys(p.timerStates).length,customBosses:p.customBosses.length,sharedBy:p.sharedBy,sharedAt:new Date(p.sharedAt).toLocaleString()}:null;return(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:b}),(0,a.jsxs)("div",{className:"relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Import Boss Timers"}),(0,a.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),p?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{className:"font-medium",children:"Share data parsed successfully!"})]}),(0,a.jsx)("p",{className:"text-sm",children:"Review the data below and choose how to import it."})]}),w&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Share Information:"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600 dark:text-gray-400",children:[w.sharedBy&&(0,a.jsxs)("div",{children:["Shared by: ",(0,a.jsx)("span",{className:"font-medium",children:w.sharedBy})]}),(0,a.jsxs)("div",{children:["Shared at: ",(0,a.jsx)("span",{className:"font-medium",children:w.sharedAt})]})]})]}),w&&(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"What will be imported:"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:w.totalBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:w.activeBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:w.customBosses}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Custom Bosses"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Import Mode"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"merge",checked:"merge"===c,onChange:e=>m(e.target.value),className:"mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:[(0,a.jsx)("strong",{children:"Merge:"})," Add new bosses and update existing timers"]})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"replace",checked:"replace"===c,onChange:e=>m(e.target.value),className:"mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:[(0,a.jsx)("strong",{children:"Replace:"})," Replace all current data with imported data"]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>u(null),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Back"}),(0,a.jsx)("button",{onClick:()=>{p&&(o({customBosses:p.customBosses,timerStates:p.timerStates,sharedBy:p.sharedBy,sharedAt:p.sharedAt}),b())},className:"flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors",children:"Import Data"})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Share Link or Code"}),(0,a.jsx)("textarea",{value:n,onChange:e=>i(e.target.value),placeholder:"Paste the share link or code here...",rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password (if required)"}),(0,a.jsx)("input",{type:"password",value:l,onChange:e=>d(e.target.value),placeholder:"Enter password if the share is protected",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),x&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded",children:x}),(0,a.jsx)("button",{onClick:()=>{if(g(),!n.trim())return;let e=n.trim();e.includes("?share=")&&(e=new URLSearchParams(e.split("?")[1]).get("share")||"");let t=y(e,l||void 0);t&&u({customBosses:t.customBosses,timerStates:t.timerStates,sharedAt:t.sharedAt,sharedBy:t.sharedBy})},disabled:h||!n.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Parsing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Preview Import"]})})]})]})]})}let B=e=>{let{onConnectionChange:t}=e,[r,o]=(0,s.useState)("testing"),[n,i]=(0,s.useState)(null),[l,d]=(0,s.useState)(!1),c=async()=>{o("testing"),i(null);try{let e=await p.testConnection();e.success?(o("connected"),null==t||t(!0)):(o("failed"),i(e.error||"Unknown connection error"),null==t||t(!1))}catch(e){o("failed"),i(e instanceof Error?e.message:"Unknown connection error"),null==t||t(!1)}},m=async()=>{d(!0),await c(),d(!1)};return((0,s.useEffect)(()=>{c()},[]),"testing"===r)?(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"text-blue-700",children:"Testing connection to Supabase..."})]}):"connected"===r?(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-green-700",children:"Connected to Supabase successfully"})]}):(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded-full"}),(0,a.jsx)("span",{className:"text-red-700 font-medium",children:"Connection Failed"})]}),n&&(0,a.jsx)("p",{className:"text-red-600 text-sm mb-3",children:n}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-red-600",children:[(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:"Possible solutions:"})}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-2",children:[(0,a.jsx)("li",{children:"Check your internet connection"}),(0,a.jsx)("li",{children:"Verify your Supabase project is active"}),(0,a.jsx)("li",{children:"Check environment variables in .env.local"}),(0,a.jsx)("li",{children:"Ensure database tables are created"})]})]}),(0,a.jsx)("button",{onClick:m,disabled:l,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Retrying...":"Retry Connection"})]})};function E(e){let{isOpen:t,onClose:r}=e,[o,n]=(0,s.useState)("create"),[i,l]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),[m,p]=(0,s.useState)(""),[u,x]=(0,s.useState)(!1),[y,g]=(0,s.useState)(!1),[b,w]=(0,s.useState)(!1),{isConnected:f,isHost:v,roomId:k,currentUser:j,connectedUsers:N,connectionStatus:R,error:S,createRoom:C,joinRoom:L,leaveRoom:E,closeRoom:A}=h(),_=async()=>{if(i.trim())try{let e=u?m.trim():void 0,t=await C(i.trim(),e);console.log("Room created:",t)}catch(e){console.error("Failed to create room:",e)}},T=async()=>{if(i.trim()&&d.trim())try{let e=y?m.trim():void 0;await L(d.trim().toUpperCase(),i.trim(),e),g(!1)}catch(e){console.error("Failed to join room:",e),e instanceof Error&&e.message.includes("password")&&g(!0)}},D=async()=>{if(k)try{await navigator.clipboard.writeText(k),w(!0),setTimeout(()=>w(!1),2e3)}catch(t){let e=document.createElement("textarea");e.value=k,document.body.appendChild(e),e.select();try{document.execCommand("copy"),w(!0),setTimeout(()=>w(!1),2e3)}catch(e){console.error("Failed to copy room code:",e)}document.body.removeChild(e)}},I=()=>{l(""),c(""),p(""),x(!1),g(!1),n("create"),w(!1),r()};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:[(0,a.jsx)("div",{className:"absolute inset-0",onClick:I}),(0,a.jsxs)("div",{className:"relative w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:f?"Real-Time Room":"Real-Time Collaboration"}),(0,a.jsx)("button",{onClick:I,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),f?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"font-medium text-green-800 dark:text-green-200",children:"Connected to Room"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-green-700 dark:text-green-300",children:"Room Code:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 rounded font-mono text-sm",children:k}),(0,a.jsx)("button",{onClick:D,className:"px-2 py-1 rounded text-xs transition-colors ".concat(b?"bg-green-600 text-white":"bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 hover:bg-green-300 dark:hover:bg-green-600"),children:b?"Copied!":"Copy"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-green-700 dark:text-green-300",children:"Your Role:"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:v?"Host":"Member"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Connected Users (",N.length,")"]}),(0,a.jsx)("div",{className:"space-y-2",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-gray-900 dark:text-white",children:[e.name,e.id===(null==j?void 0:j.id)&&" (You)"]})]},e.id))})]}),(0,a.jsx)("div",{className:"flex gap-2",children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{A(),I()},className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors",children:"Close Room"}),(0,a.jsx)("button",{onClick:I,className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Close"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{E(),I()},className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors",children:"Leave Room"}),(0,a.jsx)("button",{onClick:I,className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Close"})]})})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(B,{}),(0,a.jsxs)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>n("create"),className:"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ".concat("create"===o?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow":"text-gray-600 dark:text-gray-300"),children:"Create Room"}),(0,a.jsx)("button",{onClick:()=>n("join"),className:"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ".concat("join"===o?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow":"text-gray-600 dark:text-gray-300"),children:"Join Room"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Your Name"}),(0,a.jsx)("input",{type:"text",value:i,onChange:e=>l(e.target.value),placeholder:"Enter your name",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",maxLength:20})]}),"create"===o&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"usePassword",checked:u,onChange:e=>x(e.target.checked),className:"mr-2"}),(0,a.jsx)("label",{htmlFor:"usePassword",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password protect this room"})]}),u&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Password"}),(0,a.jsx)("input",{type:"password",value:m,onChange:e=>p(e.target.value),placeholder:"Enter room password",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",maxLength:50})]})]}),"join"===o&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Code"}),(0,a.jsx)("input",{type:"text",value:d,onChange:e=>c(e.target.value.toUpperCase()),placeholder:"Enter room code",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono",maxLength:6})]}),y&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Room Password"}),(0,a.jsx)("input",{type:"password",value:m,onChange:e=>p(e.target.value),placeholder:"Enter room password",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",maxLength:50})]})]}),S&&(0,a.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-3 py-2 rounded text-sm",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:S}),S.includes("Failed to create room")||S.includes("Failed to get room data")?(0,a.jsx)("p",{className:"text-xs mt-1",children:"Make sure Supabase is configured correctly. Check the setup guide in SUPABASE_SETUP.md"}):null]})]})}),(0,a.jsx)("button",{onClick:"create"===o?_:T,disabled:"connecting"===R||!i.trim()||"join"===o&&!d.trim(),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2",children:"connecting"===R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"animate-spin w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"create"===o?"Creating...":"Joining..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"create"===o?"Create Room":"Join Room"]})}),(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Real-Time Collaboration"}),(0,a.jsxs)("p",{children:["Share boss timers that update live across all devices. ","join"===o?"Enter the room code from the host to connect directly.":"Share your room code with others to collaborate!"]}),(0,a.jsx)("p",{className:"text-xs mt-2 opacity-75",children:"Powered by Supabase for reliable cross-device synchronization."})]})]})})]})]})]}):null}function A(e){var t,r;let{lastAction:s,lastBossAction:o}=e,{isConnected:n,roomId:i,connectedUsers:l,currentUser:d}=h();if(!n)return null;let c=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3),r=Math.floor(t/60);if(t<60)return"just now";{if(r<60)return"".concat(r,"m ago");let e=Math.floor(r/60);return"".concat(e,"h ago")}};return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Real-Time Room"})]}),(0,a.jsx)("code",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-xs font-mono",children:i})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Connected:"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[l.slice(0,5).map(e=>(0,a.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium",style:{backgroundColor:e.color},title:e.name,children:e.name.charAt(0).toUpperCase()},e.id)),l.length>5&&(0,a.jsxs)("div",{className:"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-medium",children:["+",l.length-5]})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(",l.length," user",1!==l.length?"s":"",")"]})]}),(s||o)&&(0,a.jsxs)("div",{className:"space-y-2",children:[s&&s.userId!==(null==d?void 0:d.id)&&(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:(null==(t=l.find(e=>e.id===s.userId))?void 0:t.color)||"#6B7280"}}),(0,a.jsxs)("span",{className:"text-xs text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("strong",{children:s.userName})," ",s.action]}),(0,a.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 ml-auto",children:c(s.timestamp)})]})}),o&&o.userId!==(null==d?void 0:d.id)&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:(null==(r=l.find(e=>e.id===o.userId))?void 0:r.color)||"#6B7280"}}),(0,a.jsxs)("span",{className:"text-xs text-green-700 dark:text-green-300",children:[(0,a.jsx)("strong",{children:o.userName})," ",o.action]}),(0,a.jsx)("span",{className:"text-xs text-green-600 dark:text-green-400 ml-auto",children:c(o.timestamp)})]})})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-2",children:"Timer and boss changes sync automatically with all connected users"})]})}function _(){let{timerState:e,startTimer:t,stopTimer:r,resetTimer:o,getTimeRemaining:n,isRespawned:l,stopNotificationSound:d,isPlaying:c,lastAction:m}=function(){let[e,t]=(0,s.useState)({}),[r,a]=(0,s.useState)(new Date),[o,n]=(0,s.useState)(!1),[l,d]=(0,s.useState)(null),{settings:c,showNotification:m,playNotificationSound:p,stopNotificationSound:u,isPlaying:y}=i(),{isConnected:g,currentUser:b,sendMessage:w,addMessageHandler:f,saveRoomState:v,getRoomState:k}=h();(0,s.useEffect)(()=>{n(!0)},[]),(0,s.useEffect)(()=>{if(!o)return;let e=localStorage.getItem(x);if(e)try{let r=JSON.parse(e),a={};Object.keys(r).forEach(e=>{a[e]={...r[e],lastKilled:r[e].lastKilled?new Date(r[e].lastKilled):void 0,nextRespawn:r[e].nextRespawn?new Date(r[e].nextRespawn):void 0}}),t(a)}catch(e){console.error("Failed to parse saved timer state:",e)}},[o]),(0,s.useEffect)(()=>{o&&localStorage.setItem(x,JSON.stringify(e))},[e,o]),(0,s.useEffect)(()=>{if(!g||!b||!o)return;let t=setTimeout(()=>{v("timer_state",e)},1e3);return()=>clearTimeout(t)},[e,g,b,o,v]),(0,s.useEffect)(()=>{if(g)return f(r=>{switch(console.log("Received real-time message:",r.type,"from:",r.userName),r.type){case"timer_start":{let{bossId:e,killTime:a,respawnHours:s}=r.data,o=new Date(a||r.timestamp),n=new Date(o.getTime()+60*(s||0)*6e4);t(t=>({...t,[e]:{lastKilled:o,nextRespawn:n,isActive:!0,notificationSent:!1}})),d({userId:r.userId,userName:r.userName,action:"started timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"timer_stop":{let{bossId:e}=r.data;t(t=>({...t,[e]:{...t[e],isActive:!1}})),d({userId:r.userId,userName:r.userName,action:"stopped timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"timer_reset":{let{bossId:e}=r.data;t(t=>{let r={...t};return delete r[e],r}),d({userId:r.userId,userName:r.userName,action:"reset timer for ".concat(e),timestamp:new Date(r.timestamp)});break}case"sync_request":b&&w("sync_response",{timerState:e});break;case"sync_response":{let{timerState:e}=r.data;e&&t(t=>{let r={...t};return Object.entries(e).forEach(e=>{let[t,a]=e,s=r[t],o=new Date(a.lastKilled||0),n=new Date((null==s?void 0:s.lastKilled)||0);(!s||o>n)&&(r[t]={lastKilled:a.lastKilled?new Date(a.lastKilled):void 0,nextRespawn:a.nextRespawn?new Date(a.nextRespawn):void 0,isActive:a.isActive||!1,notificationSent:a.notificationSent||!1})}),r});break}case"full_sync":{let{timerState:e,customBosses:a}=r.data;if(e){let r={};Object.entries(e).forEach(e=>{let[t,a]=e;r[t]={lastKilled:a.lastKilled?new Date(a.lastKilled):void 0,nextRespawn:a.nextRespawn?new Date(a.nextRespawn):void 0,isActive:a.isActive||!1,notificationSent:a.notificationSent||!1}}),t(r)}d({userId:r.userId,userName:r.userName,action:"synchronized room data",timestamp:new Date(r.timestamp)});break}case"room_closed":d({userId:r.userId,userName:r.userName,action:"closed the room",timestamp:new Date(r.timestamp)});break;case"user_join":b&&r.userId!==b.id&&setTimeout(()=>{w("full_sync",{timerState:e,customBosses:[]})},1e3),d({userId:r.userId,userName:r.userName,action:"joined the room",timestamp:new Date(r.timestamp)});break;case"user_leave":d({userId:r.userId,userName:r.userName,action:"left the room",timestamp:new Date(r.timestamp)})}})},[g,b,w,f,e]),(0,s.useEffect)(()=>{g&&b&&o&&k("timer_state").then(e=>{if(e.length>0){let r=e[0].state_data;if(r){let e={};Object.entries(r).forEach(t=>{let[r,a]=t;e[r]={lastKilled:a.lastKilled?new Date(a.lastKilled):void 0,nextRespawn:a.nextRespawn?new Date(a.nextRespawn):void 0,isActive:a.isActive||!1,notificationSent:a.notificationSent||!1}}),console.log("Loaded timer state from database:",e),t(e)}}else w("sync_request",{})}).catch(e=>{console.error("Failed to load timer state from database:",e),w("sync_request",{})})},[g,b,o,k,w]);let j=(0,s.useCallback)(r=>{Object.entries(e).forEach(e=>{let[a,s]=e;if(!s.isActive||!s.nextRespawn)return;let o=s.nextRespawn.getTime()-r.getTime(),n=Math.floor(o/6e4);if(o<=0&&!s.notificationSent){m("Boss Respawned!",{body:"".concat(a," has respawned and is ready to hunt!"),tag:"boss-respawned-".concat(a)}),p(),t(e=>({...e,[a]:{...e[a],notificationSent:!0}}));return}c.warningMinutes.forEach(e=>{n!==e||s.notificationSent||(m("Boss Respawn Warning",{body:"".concat(a," will respawn in ").concat(e," minutes!"),tag:"boss-warning-".concat(a,"-").concat(e)}),p())})})},[e,c.warningMinutes,m,p]);(0,s.useEffect)(()=>{let e=setInterval(()=>{let e=new Date;a(e),c.enabled&&j(e)},1e3);return()=>clearInterval(e)},[c.enabled,j]);let N=(0,s.useCallback)((e,r,a)=>{let s=a||new Date,o=new Date(s.getTime()+60*r*6e4);console.log("Starting timer for boss:",e,"isConnected:",g),t(t=>({...t,[e]:{lastKilled:s,nextRespawn:o,isActive:!0,notificationSent:!1}})),g?(console.log("Sending timer_start message for boss:",e),w("timer_start",{bossId:e,killTime:s,respawnHours:r})):console.log("Not connected, timer_start message not sent")},[g,w]),R=(0,s.useCallback)(e=>{t(t=>({...t,[e]:{...t[e],isActive:!1}})),g&&w("timer_stop",{bossId:e})},[g,w]),S=(0,s.useCallback)(e=>{t(t=>{let r={...t};return delete r[e],r}),g&&w("timer_reset",{bossId:e})},[g,w]),C=(0,s.useCallback)(t=>{let a=e[t];if(!(null==a?void 0:a.isActive)||!a.nextRespawn)return null;let s=r.getTime(),o=a.nextRespawn.getTime()-s;if(o<=0)return"Respawned!";let n=Math.floor(o/36e5),i=Math.floor(o%36e5/6e4),l=Math.floor(o%6e4/1e3);return n>0?"".concat(n,":").concat(i.toString().padStart(2,"0"),":").concat(l.toString().padStart(2,"0")):"".concat(i,":").concat(l.toString().padStart(2,"0"))},[e,r]),L=(0,s.useCallback)(t=>{let a=e[t];return!!(null==a?void 0:a.isActive)&&!!a.nextRespawn&&a.nextRespawn.getTime()<=r.getTime()},[e,r]);return{timerState:e,currentTime:r,lastAction:l,startTimer:N,stopTimer:R,resetTimer:S,getTimeRemaining:C,isRespawned:L,stopNotificationSound:u,isPlaying:y}}(),{allBosses:p,customBosses:u,addBoss:b,updateBoss:w,deleteBoss:N,isCustomBoss:R,lastBossAction:S}=function(){let{isConnected:e,currentUser:t,sendMessage:r,addMessageHandler:a,isHost:o,saveRoomState:n,getRoomState:i}=h(),{allBosses:l,customBosses:d,addBoss:c,updateBoss:m,deleteBoss:p,isCustomBoss:u,getBossById:x}=function(){let[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a(!0)},[]),(0,s.useEffect)(()=>{if(!r)return;let e=localStorage.getItem(g);if(e)try{let r=JSON.parse(e);t(r)}catch(e){console.error("Failed to parse saved custom bosses:",e)}},[r]),(0,s.useEffect)(()=>{r&&localStorage.setItem(g,JSON.stringify(e))},[e,r]);let o=(0,s.useCallback)(()=>[...y,...e],[e]),n=(0,s.useCallback)(e=>{t(t=>[...t,e])},[]),i=(0,s.useCallback)((e,r)=>{t(t=>t.map(t=>t.id===e?{...t,...r}:t))},[]),l=(0,s.useCallback)(e=>!!e.startsWith("custom-")&&(t(t=>t.filter(t=>t.id!==e)),!0),[]),d=(0,s.useCallback)(e=>e.startsWith("custom-"),[]),c=(0,s.useCallback)(e=>o().find(t=>t.id===e),[o]);return{allBosses:o(),customBosses:e,addBoss:n,updateBoss:i,deleteBoss:l,isCustomBoss:d,getBossById:c}}(),[b,w]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(e)return a(e=>{switch(console.log("Received boss message:",e.type,"from:",e.userName),e.type){case"boss_add":{let{boss:t}=e.data;t&&(c(t),w({userId:e.userId,userName:e.userName,action:"added boss ".concat(t.name),timestamp:new Date(e.timestamp)}));break}case"boss_update":{let t=e.data;t.bossId&&t.updates&&(m(t.bossId,t.updates),w({userId:e.userId,userName:e.userName,action:"updated boss ".concat(t.updates.name||t.bossId),timestamp:new Date(e.timestamp)}));break}case"boss_delete":{let t=e.data;if(t.bossId){let r=x(t.bossId);p(t.bossId)&&w({userId:e.userId,userName:e.userName,action:"deleted boss ".concat((null==r?void 0:r.name)||t.bossId),timestamp:new Date(e.timestamp)})}break}case"full_sync":{let{customBosses:t}=e.data;t&&Array.isArray(t)&&(t.forEach(e=>{x(e.id)||c(e)}),w({userId:e.userId,userName:e.userName,action:"synchronized ".concat(t.length," custom bosses"),timestamp:new Date(e.timestamp)}));break}case"user_join":o&&t&&e.userId!==t.id&&setTimeout(()=>{r("full_sync",{customBosses:d})},1500)}})},[e,t,r,a,o,d,c,m,p,x]),(0,s.useEffect)(()=>{if(!e||!t)return;let r=setTimeout(()=>{n("custom_bosses",d)},1e3);return()=>clearTimeout(r)},[d,e,t,n]),(0,s.useEffect)(()=>{e&&t&&i("custom_bosses").then(e=>{if(e.length>0){let t=e[0].state_data;t&&Array.isArray(t)&&(console.log("Loaded custom bosses from database:",t),t.forEach(e=>{c(e)}))}}).catch(e=>{console.error("Failed to load custom bosses from database:",e)})},[e,t,i,c]);let f=(0,s.useCallback)(t=>{c(t),e&&r("boss_add",{boss:t})},[c,e,r]);return{allBosses:l,customBosses:d,isCustomBoss:u,getBossById:x,addBoss:f,updateBoss:(0,s.useCallback)((t,a)=>{m(t,a),e&&r("boss_update",{action:"update",bossId:t,updates:a,timestamp:new Date})},[m,e,r]),deleteBoss:(0,s.useCallback)(t=>{let a=p(t);return a&&e&&r("boss_delete",{action:"delete",bossId:t,timestamp:new Date}),a},[p,e,r]),lastBossAction:b}}(),[B,_]=(0,s.useState)("name"),[T,D]=(0,s.useState)("all"),[I,M]=(0,s.useState)(""),[P,H]=(0,s.useState)(null),[V,F]=(0,s.useState)(""),[q,W]=(0,s.useState)(null),[U,O]=(0,s.useState)(!1),[z,K]=(0,s.useState)(!1),[J,G]=(0,s.useState)(!1),[Y,Q]=(0,s.useState)(null),[Z,X]=(0,s.useState)(!1),[$,ee]=(0,s.useState)(!1),[et,er]=(0,s.useState)(!1),[ea,es]=(0,s.useState)(!1);(0,s.useEffect)(()=>{es(!0),new URLSearchParams(window.location.search).get("share")&&ee(!0)},[]);let eo=p.filter(t=>{var r,a;if("active"===T&&!(null==(r=e[t.id])?void 0:r.isActive)||"inactive"===T&&(null==(a=e[t.id])?void 0:a.isActive))return!1;if(I){let e=I.toLowerCase(),r=t.name.toLowerCase().includes(e),a=t.location.toLowerCase().includes(e);if(!r&&!a)return!1}return!0}).sort((t,r)=>{switch(B){case"name":return t.name.localeCompare(r.name);case"level":return t.level-r.level;case"respawnTime":return t.respawnTime-r.respawnTime;case"location":return t.location.localeCompare(r.location);case"timeOfDeath":var a,s,o,n;let i=(null==(s=e[t.id])||null==(a=s.lastKilled)?void 0:a.getTime())||0;return((null==(n=e[r.id])||null==(o=n.lastKilled)?void 0:o.getTime())||0)-i;default:return 0}}),en=e=>{t(e.id,e.respawnTime)},ei=e=>{H(e.id);let t=new Date;F(new Date(t.getTime()-6e4*t.getTimezoneOffset()).toISOString().slice(0,16))},el=e=>{if(V){let r=new Date(V);t(e.id,e.respawnTime,r),H(null),F("")}},ed=()=>{H(null),F("")},ec=e=>{r(e.id)},em=e=>{o(e.id)},ep=e=>{Q(e),G(!0)},eu=e=>{window.confirm('Are you sure you want to delete "'.concat(e.name,'"? This action cannot be undone.'))&&(N(e.id)?o(e.id):alert("Cannot delete default bosses. Only custom bosses can be deleted."))},eh=t=>{let r=e[t.id];if(!(null==r?void 0:r.isActive))return"bg-gray-100 dark:bg-gray-800";if(l(t.id))return"bg-green-100 dark:bg-green-900";if(r.nextRespawn){let e=Math.floor((r.nextRespawn.getTime()-new Date().getTime())/6e4);if(e<=5)return"bg-red-100 dark:bg-red-900";if(e<=30)return"bg-yellow-100 dark:bg-yellow-900"}return"bg-blue-100 dark:bg-blue-900"},ex=t=>{let r=e[t.id];if(!(null==r?void 0:r.isActive))return"Inactive";if(l(t.id))return"Respawned!";if(r.nextRespawn){let e=Math.floor((r.nextRespawn.getTime()-new Date().getTime())/6e4);if(e<=5)return"Soon!";if(e<=30)return"Warning"}return"Active"},ey=t=>{let r=e[t.id];if(!(null==r?void 0:r.lastKilled))return"-";let s=r.lastKilled,o=s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!0}),n=s.toLocaleDateString([],{month:"short",day:"numeric"}),i=new Date().getTime()-s.getTime(),l=Math.floor(i/36e5),d=Math.floor(i%36e5/6e4),c="";c=l>0?"".concat(l,"h ").concat(d,"m ago"):d>0?"".concat(d,"m ago"):"Just now";let m=new Date;return s.toDateString()===m.toDateString()?(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsx)("div",{className:"font-medium",children:o}),(0,a.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:c})]}):(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"font-medium",children:[n," ",o]}),(0,a.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:c})]})};return ea?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Search"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",value:I,onChange:e=>M(e.target.value),placeholder:"Search by name or location...",className:"pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-64"}),(0,a.jsx)("svg",{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Sort by"}),(0,a.jsxs)("select",{value:B,onChange:e=>_(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,a.jsx)("option",{value:"name",children:"Name"}),(0,a.jsx)("option",{value:"level",children:"Level"}),(0,a.jsx)("option",{value:"respawnTime",children:"Respawn Time"}),(0,a.jsx)("option",{value:"location",children:"Location"}),(0,a.jsx)("option",{value:"timeOfDeath",children:"Time of Death"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:T,onChange:e=>D(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>K(!0),className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add Boss"]}),c&&(0,a.jsxs)("button",{onClick:d,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center gap-2 animate-pulse",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 11-6.219-8.56"})}),"Stop Sound"]}),(0,a.jsxs)("button",{onClick:()=>X(!0),className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),"Share"]}),(0,a.jsxs)("button",{onClick:()=>er(!0),className:"px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Live Share"]}),(0,a.jsxs)("button",{onClick:()=>ee(!0),className:"px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}),"Import"]}),(0,a.jsxs)("button",{onClick:()=>O(!0),className:"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"})}),"Notifications"]})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Boss"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Respawn Time"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Time Remaining"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Time of Death"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-600",children:eo.map(t=>{var r,s;return(0,a.jsxs)("tr",{className:"".concat(eh(t)," transition-colors hover:bg-opacity-80"),children:[(0,a.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:t.location}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>W(t),className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors",children:t.name}),R(t.id)&&(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200",children:"Custom"})]})}),(0,a.jsxs)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:["Lv. ",t.level]}),(0,a.jsxs)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:[t.respawnTime,"h",t.respawnVariance&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-1",children:["(\xb1",t.respawnVariance,"m)"]})]}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((null==(r=e[t.id])?void 0:r.isActive)?l(t.id)?"bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200":"bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200":"bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200"),children:ex(t)})}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm font-mono text-gray-900 dark:text-white",children:n(t.id)||"-"}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:ey(t)}),(0,a.jsx)("td",{className:"px-4 py-4 text-sm",children:P===t.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"datetime-local",value:V,onChange:e=>F(e.target.value),className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>el(t),className:"px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors",children:"✓"}),(0,a.jsx)("button",{onClick:ed,className:"px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded transition-colors",children:"✕"})]})]}):(null==(s=e[t.id])?void 0:s.isActive)?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("button",{onClick:()=>ec(t),className:"px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded-md transition-colors",children:"Stop"}),(0,a.jsx)("button",{onClick:()=>em(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Reset"})]}),R(t.id)&&(0,a.jsxs)("div",{className:"flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>ep(t),className:"px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>eu(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Delete"})]})]}):(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("button",{onClick:()=>en(t),className:"px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-md transition-colors",children:"Start Now"}),(0,a.jsx)("button",{onClick:()=>ei(t),className:"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors",children:"Set Time"})]}),R(t.id)&&(0,a.jsxs)("div",{className:"flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>ep(t),className:"px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>eu(t),className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors",children:"Delete"})]})]})})]},t.id)})})]})})}),(0,a.jsx)(A,{lastAction:m,lastBossAction:S}),c&&(0,a.jsxs)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg flex items-center justify-between animate-pulse",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6m-6 0a3 3 0 106 0m-6 0a3 3 0 116 0"})}),(0,a.jsx)("span",{className:"font-medium",children:"Boss notification sound is playing!"})]}),(0,a.jsx)("button",{onClick:d,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors",children:"Stop Sound"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:p.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Bosses"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:Object.values(e).filter(e=>e.isActive).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Active Timers"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:p.filter(e=>l(e.id)).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Ready to Hunt"})]})]}),(0,a.jsx)(f,{boss:q,isOpen:!!q,onClose:()=>W(null)}),(0,a.jsx)(k,{isOpen:z,onClose:()=>K(!1),onAddBoss:b}),(0,a.jsx)(j,{isOpen:J,onClose:()=>{G(!1),Q(null)},onUpdateBoss:e=>{w(e.id,e),G(!1),Q(null)},boss:Y}),(0,a.jsx)(C,{isOpen:Z,onClose:()=>X(!1),customBosses:u,timerStates:e}),(0,a.jsx)(L,{isOpen:$,onClose:()=>ee(!1),onImport:e=>{e.customBosses.forEach(e=>{b(e)}),Object.entries(e.timerStates).forEach(e=>{let[r,a]=e;if(a.isActive&&a.lastKilled&&a.respawnHours){let e=new Date(a.lastKilled);t(r,a.respawnHours,e)}}),alert("Successfully imported ".concat(e.customBosses.length," custom bosses and timer states!").concat(e.sharedBy?" (Shared by: ".concat(e.sharedBy,")"):""))}}),(0,a.jsx)(v,{isOpen:U,onClose:()=>O(!1)}),(0,a.jsx)(E,{isOpen:et,onClose:()=>er(!1)})]}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,a.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Loading boss timer..."})})})}},4684:(e,t,r)=>{Promise.resolve().then(r.bind(r,2015))}},e=>{var t=t=>e(e.s=t);e.O(0,[982,441,684,358],()=>t(4684)),_N_E=e.O()}]);