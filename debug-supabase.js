// Debug script to test Supabase connection and identify 406 error
// Run with: node debug-supabase.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Debugging Supabase Connection...');
console.log('URL:', supabaseUrl);
console.log('Key exists:', !!supabaseAnonKey);
console.log('Key length:', supabaseAnonKey?.length);

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables!');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client with debug options
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  },
});

async function testConnection() {
  console.log('\n📡 Testing basic connection...');
  
  try {
    // Test 1: Basic connection test
    const { error: basicError } = await supabase
      .from('rooms')
      .select('count')
      .limit(1);

    if (basicError) {
      console.error('❌ Basic connection failed:', basicError);
      return false;
    }
    console.log('✅ Basic connection successful');

    // Test 2: Test room_users table access
    console.log('\n📋 Testing room_users table access...');
    const { data: users, error: usersError } = await supabase
      .from('room_users')
      .select('id')
      .limit(1);

    if (usersError) {
      console.error('❌ room_users table access failed:', usersError);
      return false;
    }
    console.log('✅ room_users table accessible');

    // Test 3: Test the specific query that was failing
    console.log('\n🔍 Testing the problematic query...');
    const testRoomId = 'U8G0AM';
    const testUserId = 'rlz0bjx80u';
    
    console.log(`Testing query: room_id=eq.${testRoomId}&user_id=eq.${testUserId}`);
    
    // First try with .single() (the problematic approach)
    try {
      const { data: singleResult, error: singleError } = await supabase
        .from('room_users')
        .select('id')
        .eq('room_id', testRoomId)
        .eq('user_id', testUserId)
        .single();
        
      if (singleError) {
        console.log('⚠️  .single() query failed (expected):', singleError.code, singleError.message);
      } else {
        console.log('✅ .single() query succeeded:', singleResult);
      }
    } catch (err) {
      console.log('⚠️  .single() query threw exception:', err.message);
    }

    // Now try without .single() (the fixed approach)
    const { data: listResult, error: listError } = await supabase
      .from('room_users')
      .select('id')
      .eq('room_id', testRoomId)
      .eq('user_id', testUserId)
      .limit(1);

    if (listError) {
      console.error('❌ List query also failed:', listError);
      return false;
    }
    console.log('✅ List query succeeded. Results:', listResult?.length || 0, 'rows');

    // Test 4: Test creating a test user entry
    console.log('\n➕ Testing user insertion...');
    const testUser = {
      room_id: 'TEST_ROOM',
      user_id: 'test_user_' + Date.now(),
      user_name: 'Test User',
      joined_at: new Date().toISOString(),
      last_seen: new Date().toISOString(),
      is_online: true,
    };

    const { data: insertResult, error: insertError } = await supabase
      .from('room_users')
      .insert(testUser)
      .select();

    if (insertError) {
      console.error('❌ User insertion failed:', insertError);
      return false;
    }
    console.log('✅ User insertion successful');

    // Clean up test data
    if (insertResult && insertResult.length > 0) {
      await supabase
        .from('room_users')
        .delete()
        .eq('id', insertResult[0].id);
      console.log('🧹 Test data cleaned up');
    }

    return true;
  } catch (error) {
    console.error('❌ Connection test failed with exception:', error);
    return false;
  }
}

async function testRoomOperations() {
  console.log('\n🏠 Testing room operations...');
  
  const testRoomId = 'TEST_' + Date.now();
  
  try {
    // Test room creation
    const { data: roomData, error: createError } = await supabase
      .from('rooms')
      .insert({
        id: testRoomId,
        host_id: 'test_host',
        host_name: 'Test Host',
        has_password: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_activity: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Room creation failed:', createError);
      return false;
    }
    console.log('✅ Room creation successful');

    // Test room retrieval
    const { data: retrievedRoom, error: retrieveError } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', testRoomId)
      .single();

    if (retrieveError) {
      console.error('❌ Room retrieval failed:', retrieveError);
    } else {
      console.log('✅ Room retrieval successful');
    }

    // Clean up
    await supabase
      .from('rooms')
      .delete()
      .eq('id', testRoomId);
    console.log('🧹 Test room cleaned up');

    return true;
  } catch (error) {
    console.error('❌ Room operations test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const connectionOk = await testConnection();
  const roomOpsOk = await testRoomOperations();
  
  console.log('\n📊 Test Results:');
  console.log('Connection test:', connectionOk ? '✅ PASS' : '❌ FAIL');
  console.log('Room operations test:', roomOpsOk ? '✅ PASS' : '❌ FAIL');
  
  if (connectionOk && roomOpsOk) {
    console.log('\n🎉 All tests passed! The 406 error should be resolved.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the error messages above.');
  }
}

runAllTests().catch(console.error);
