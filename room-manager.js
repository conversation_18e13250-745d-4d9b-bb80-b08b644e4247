// Room management utility script
// Run with: node room-manager.js [command] [args...]

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function listRooms() {
  console.log('📋 Active Rooms:');
  const { data, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No active rooms found.');
    return;
  }

  data.forEach(room => {
    console.log(`\n🏠 Room: ${room.id}`);
    console.log(`   Host: ${room.host_name} (${room.host_id})`);
    console.log(`   Password: ${room.has_password ? 'Yes' : 'No'}`);
    console.log(`   Created: ${new Date(room.created_at).toLocaleString()}`);
    console.log(`   Last Activity: ${new Date(room.last_activity).toLocaleString()}`);
  });
}

async function listRoomUsers(roomId) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`👥 Users in room ${roomId}:`);
  const { data, error } = await supabase
    .from('room_users')
    .select('*')
    .eq('room_id', roomId)
    .order('joined_at', { ascending: true });

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No users found in this room.');
    return;
  }

  data.forEach(user => {
    const status = user.is_online ? '🟢 Online' : '🔴 Offline';
    console.log(`\n${status} ${user.user_name} (${user.user_id})`);
    console.log(`   Joined: ${new Date(user.joined_at).toLocaleString()}`);
    console.log(`   Last Seen: ${new Date(user.last_seen).toLocaleString()}`);
  });
}

async function cleanupRoom(roomId) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`🧹 Cleaning up room ${roomId}...`);
  
  // Mark all users as offline
  const { error: usersError } = await supabase
    .from('room_users')
    .update({ is_online: false, last_seen: new Date().toISOString() })
    .eq('room_id', roomId);

  if (usersError) {
    console.error('❌ Error updating users:', usersError);
    return;
  }

  // Deactivate room
  const { error: roomError } = await supabase
    .from('rooms')
    .update({ is_active: false, updated_at: new Date().toISOString() })
    .eq('id', roomId);

  if (roomError) {
    console.error('❌ Error deactivating room:', roomError);
    return;
  }

  console.log('✅ Room cleaned up successfully');
}

async function removeUser(roomId, userId) {
  if (!roomId || !userId) {
    console.error('❌ Please provide both room ID and user ID');
    return;
  }

  console.log(`🚪 Removing user ${userId} from room ${roomId}...`);
  
  const { error } = await supabase
    .from('room_users')
    .update({ is_online: false, last_seen: new Date().toISOString() })
    .eq('room_id', roomId)
    .eq('user_id', userId);

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  console.log('✅ User removed successfully');
}

async function getRoomMessages(roomId, limit = 10) {
  if (!roomId) {
    console.error('❌ Please provide a room ID');
    return;
  }

  console.log(`💬 Recent messages in room ${roomId}:`);
  const { data, error } = await supabase
    .from('room_messages')
    .select('*')
    .eq('room_id', roomId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('❌ Error:', error);
    return;
  }

  if (!data || data.length === 0) {
    console.log('No messages found in this room.');
    return;
  }

  data.reverse().forEach(msg => {
    const time = new Date(msg.created_at).toLocaleTimeString();
    console.log(`\n[${time}] ${msg.user_name}: ${msg.message_type}`);
    if (msg.message_data && Object.keys(msg.message_data).length > 0) {
      console.log(`   Data: ${JSON.stringify(msg.message_data, null, 2)}`);
    }
  });
}

function showHelp() {
  console.log(`
🛠️  Room Manager Utility

Usage: node room-manager.js [command] [args...]

Commands:
  list-rooms                    List all active rooms
  list-users <roomId>          List users in a specific room
  cleanup-room <roomId>        Mark room as inactive and all users as offline
  remove-user <roomId> <userId> Remove a specific user from a room
  messages <roomId> [limit]    Show recent messages in a room (default: 10)
  help                         Show this help message

Examples:
  node room-manager.js list-rooms
  node room-manager.js list-users U8G0AM
  node room-manager.js cleanup-room U8G0AM
  node room-manager.js remove-user U8G0AM rlz0bjx80u
  node room-manager.js messages U8G0AM 20
`);
}

// Main execution
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];
const arg3 = process.argv[5];

switch (command) {
  case 'list-rooms':
    await listRooms();
    break;
  case 'list-users':
    await listRoomUsers(arg1);
    break;
  case 'cleanup-room':
    await cleanupRoom(arg1);
    break;
  case 'remove-user':
    await removeUser(arg1, arg2);
    break;
  case 'messages':
    await getRoomMessages(arg1, arg2 ? parseInt(arg2) : 10);
    break;
  case 'help':
  default:
    showHelp();
    break;
}
