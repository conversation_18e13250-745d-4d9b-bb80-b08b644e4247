-- L2M Boss Timer - Supabase Database Schema
-- Run this SQL in your Supabase SQL editor to create the required tables

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create rooms table
CREATE TABLE IF NOT EXISTS public.rooms (
    id TEXT PRIMARY KEY,
    host_id TEXT NOT NULL,
    host_name TEXT NOT NULL,
    password_hash TEXT,
    has_password BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_users table
CREATE TABLE IF NOT EXISTS public.room_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id TEXT NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    user_name TEXT NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_online BOOLEAN DEFAULT TRUE,
    UNIQUE(room_id, user_id)
);

-- Create room_messages table
CREATE TABLE IF NOT EXISTS public.room_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id TEXT NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    user_name TEXT NOT NULL,
    message_type TEXT NOT NULL,
    message_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_state table for persistent boss timer data
CREATE TABLE IF NOT EXISTS public.room_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id TEXT NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
    state_type TEXT NOT NULL, -- 'timer_state' or 'custom_bosses'
    state_data JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT NOT NULL,
    UNIQUE(room_id, state_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rooms_host_id ON public.rooms(host_id);
CREATE INDEX IF NOT EXISTS idx_rooms_is_active ON public.rooms(is_active);
CREATE INDEX IF NOT EXISTS idx_room_users_room_id ON public.room_users(room_id);
CREATE INDEX IF NOT EXISTS idx_room_users_is_online ON public.room_users(is_online);
CREATE INDEX IF NOT EXISTS idx_room_messages_room_id ON public.room_messages(room_id);
CREATE INDEX IF NOT EXISTS idx_room_messages_created_at ON public.room_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_room_state_room_id ON public.room_state(room_id);
CREATE INDEX IF NOT EXISTS idx_room_state_type ON public.room_state(state_type);

-- Enable Row Level Security (RLS)
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_state ENABLE ROW LEVEL SECURITY;

-- RLS Policies for rooms table
CREATE POLICY "Anyone can read active rooms" ON public.rooms
    FOR SELECT USING (is_active = true);

CREATE POLICY "Anyone can create rooms" ON public.rooms
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Host can update their rooms" ON public.rooms
    FOR UPDATE USING (true);

CREATE POLICY "Host can delete their rooms" ON public.rooms
    FOR DELETE USING (true);

-- RLS Policies for room_users table
CREATE POLICY "Anyone can read room users" ON public.room_users
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert room users" ON public.room_users
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own presence" ON public.room_users
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete their own presence" ON public.room_users
    FOR DELETE USING (true);

-- RLS Policies for room_messages table
CREATE POLICY "Anyone can read room messages" ON public.room_messages
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert room messages" ON public.room_messages
    FOR INSERT WITH CHECK (true);

-- RLS Policies for room_state table
CREATE POLICY "Anyone can read room state" ON public.room_state
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert room state" ON public.room_state
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update room state" ON public.room_state
    FOR UPDATE USING (true);

CREATE POLICY "Anyone can delete room state" ON public.room_state
    FOR DELETE USING (true);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on rooms table
CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON public.rooms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up old inactive users (optional)
CREATE OR REPLACE FUNCTION cleanup_inactive_users()
RETURNS void AS $$
BEGIN
    -- Mark users as offline if they haven't been seen for more than 5 minutes
    UPDATE public.room_users 
    SET is_online = false 
    WHERE last_seen < NOW() - INTERVAL '5 minutes' 
    AND is_online = true;
    
    -- Delete old messages (older than 24 hours)
    DELETE FROM public.room_messages 
    WHERE created_at < NOW() - INTERVAL '24 hours';
    
    -- Delete inactive rooms (no activity for more than 24 hours)
    DELETE FROM public.rooms 
    WHERE last_activity < NOW() - INTERVAL '24 hours' 
    AND is_active = false;
END;
$$ language 'plpgsql';

-- Enable realtime for all tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.rooms;
ALTER PUBLICATION supabase_realtime ADD TABLE public.room_users;
ALTER PUBLICATION supabase_realtime ADD TABLE public.room_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.room_state;

-- Optional: Create a scheduled job to run cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-inactive-users', '*/5 * * * *', 'SELECT cleanup_inactive_users();');

-- Grant necessary permissions
GRANT ALL ON public.rooms TO anon, authenticated;
GRANT ALL ON public.room_users TO anon, authenticated;
GRANT ALL ON public.room_messages TO anon, authenticated;
GRANT ALL ON public.room_state TO anon, authenticated;
