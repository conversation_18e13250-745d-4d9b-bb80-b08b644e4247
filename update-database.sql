-- Add room_state table for persistent boss timer data
CREATE TABLE IF NOT EXISTS public.room_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id TEXT NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
    state_type TEXT NOT NULL, -- 'timer_state' or 'custom_bosses'
    state_data JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT NOT NULL,
    UNIQUE(room_id, state_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_room_state_room_id ON public.room_state(room_id);
CREATE INDEX IF NOT EXISTS idx_room_state_type ON public.room_state(state_type);

-- Enable Row Level Security (RLS)
ALTER TABLE public.room_state ENABLE ROW LEVEL SECURITY;

-- RLS Policies for room_state table
CREATE POLICY "Anyone can read room state" ON public.room_state
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert room state" ON public.room_state
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update room state" ON public.room_state
    FOR UPDATE USING (true);

CREATE POLICY "Anyone can delete room state" ON public.room_state
    FOR DELETE USING (true);

-- Enable realtime for room_state table
ALTER PUBLICATION supabase_realtime ADD TABLE public.room_state;

-- Grant necessary permissions
GRANT ALL ON public.room_state TO anon, authenticated;
