// Simple Node.js script to test Supabase connection
// Run with: node test-supabase-connection.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Testing Supabase Connection...');
console.log('URL:', supabaseUrl);
console.log('Key exists:', !!supabaseAnonKey);

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables!');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('\n🔍 Testing basic connection...');
    const { data, error } = await supabase
      .from('rooms')
      .select('count')
      .limit(1);

    if (error) {
      console.error('❌ Connection failed:', error.message);
      console.error('Full error:', error);
      return false;
    }

    console.log('✅ Connection successful!');
    return true;
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
}

async function testTables() {
  console.log('\n🔍 Testing table access...');
  
  const tables = ['rooms', 'room_users', 'room_messages'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
        
      if (error) {
        console.error(`❌ Table '${table}' error:`, error.message);
      } else {
        console.log(`✅ Table '${table}' accessible`);
      }
    } catch (error) {
      console.error(`❌ Table '${table}' error:`, error.message);
    }
  }
}

async function main() {
  const connectionOk = await testConnection();
  
  if (connectionOk) {
    await testTables();
    console.log('\n🎉 Supabase connection test completed!');
  } else {
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Check your internet connection');
    console.log('2. Verify your Supabase project is active');
    console.log('3. Check that the environment variables are correct');
    console.log('4. Make sure the database tables are created');
    console.log('5. Check if your Supabase project has any billing issues');
  }
}

main().catch(console.error);
