'use client';

import { useState, useEffect } from 'react';
import { useRealTimeSharing } from '@/hooks/useRealTimeSharing';
import { SupabaseConnectionTest } from './SupabaseConnectionTest';

interface RealTimeShareModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function RealTimeShareModal({ isOpen, onClose }: RealTimeShareModalProps) {
  const [mode, setMode] = useState<'create' | 'join'>('create');
  const [userName, setUserName] = useState('');
  const [roomCode, setRoomCode] = useState('');
  const [password, setPassword] = useState('');
  const [usePassword, setUsePassword] = useState(false);
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(false);
  const [copied, setCopied] = useState(false);


  const {
    isConnected,
    isHost,
    roomId,
    currentUser,
    connectedUsers,
    connectionStatus,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    closeRoom,

  } = useRealTimeSharing();

  const handleCreateRoom = async () => {
    if (!userName.trim()) return;

    try {
      const roomPassword = usePassword ? password.trim() : undefined;
      const newRoomId = await createRoom(userName.trim(), roomPassword);
      console.log('Room created:', newRoomId);
    } catch (error) {
      console.error('Failed to create room:', error);
    }
  };

  const handleJoinRoom = async () => {
    if (!userName.trim() || !roomCode.trim()) return;

    try {
      const roomPassword = showPasswordPrompt ? password.trim() : undefined;
      await joinRoom(roomCode.trim().toUpperCase(), userName.trim(), roomPassword);
      setShowPasswordPrompt(false);
    } catch (error) {
      console.error('Failed to join room:', error);
      // If error mentions password, show password prompt
      if (error instanceof Error && error.message.includes('password')) {
        setShowPasswordPrompt(true);
      }
    }
  };

  const handleCopyRoomCode = async () => {
    if (!roomId) return;
    
    try {
      await navigator.clipboard.writeText(roomId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = roomId;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackError) {
        console.error('Failed to copy room code:', fallbackError);
      }
      document.body.removeChild(textArea);
    }
  };



  const handleClose = () => {
    setUserName('');
    setRoomCode('');
    setPassword('');
    setUsePassword(false);
    setShowPasswordPrompt(false);
    setMode('create');
    setCopied(false);

    onClose();
  };

  const handleLeaveRoom = () => {
    leaveRoom();
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      {/* Background overlay */}
      <div 
        className="absolute inset-0"
        onClick={handleClose}
      />

      {/* Modal panel */}
      <div className="relative w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            {isConnected ? 'Real-Time Room' : 'Real-Time Collaboration'}
          </h3>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {!isConnected ? (
          /* Connection Setup */
          <div className="space-y-4">
            {/* Supabase Connection Test */}
            <SupabaseConnectionTest />

            {/* Mode Selection */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setMode('create')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  mode === 'create'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                    : 'text-gray-600 dark:text-gray-300'
                }`}
              >
                Create Room
              </button>
              <button
                onClick={() => setMode('join')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  mode === 'join'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                    : 'text-gray-600 dark:text-gray-300'
                }`}
              >
                Join Room
              </button>
            </div>

            {/* User Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Your Name
              </label>
              <input
                type="text"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                placeholder="Enter your name"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                maxLength={20}
              />
            </div>

            {/* Password Protection (Create mode only) */}
            {mode === 'create' && (
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="usePassword"
                    checked={usePassword}
                    onChange={(e) => setUsePassword(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="usePassword" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Password protect this room
                  </label>
                </div>

                {usePassword && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Room Password
                    </label>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter room password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      maxLength={50}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Room Code (for join mode) */}
            {mode === 'join' && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Room Code
                  </label>
                  <input
                    type="text"
                    value={roomCode}
                    onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
                    placeholder="Enter room code"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono"
                    maxLength={6}
                  />
                </div>

                {/* Password field for joining */}
                {showPasswordPrompt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Room Password
                    </label>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter room password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      maxLength={50}
                    />
                  </div>
                )}


              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-3 py-2 rounded text-sm">
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="font-medium">{error}</p>
                    {error.includes('Failed to create room') || error.includes('Failed to get room data') ? (
                      <p className="text-xs mt-1">
                        Make sure Supabase is configured correctly. Check the setup guide in SUPABASE_SETUP.md
                      </p>
                    ) : null}
                  </div>
                </div>
              </div>
            )}

            {/* Action Button */}
            <button
              onClick={mode === 'create' ? handleCreateRoom : handleJoinRoom}
              disabled={
                connectionStatus === 'connecting' || 
                !userName.trim() || 
                (mode === 'join' && !roomCode.trim())
              }
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2"
            >
              {connectionStatus === 'connecting' ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {mode === 'create' ? 'Creating...' : 'Joining...'}
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  {mode === 'create' ? 'Create Room' : 'Join Room'}
                </>
              )}
            </button>

            {/* Info */}
            <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  <p className="font-medium mb-1">Real-Time Collaboration</p>
                  <p>Share boss timers that update live across all devices. {mode === 'join' ? 'Enter the room code from the host to connect directly.' : 'Share your room code with others to collaborate!'}</p>
                  <p className="text-xs mt-2 opacity-75">
                    Powered by Supabase for reliable cross-device synchronization.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Connected State */
          <div className="space-y-4">
            {/* Room Info */}
            <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-medium text-green-800 dark:text-green-200">
                  Connected to Room
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-700 dark:text-green-300">Room Code:</span>
                  <div className="flex items-center gap-2">
                    <code className="px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 rounded font-mono text-sm">
                      {roomId}
                    </code>
                    <button
                      onClick={handleCopyRoomCode}
                      className={`px-2 py-1 rounded text-xs transition-colors ${
                        copied 
                          ? 'bg-green-600 text-white' 
                          : 'bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 hover:bg-green-300 dark:hover:bg-green-600'
                      }`}
                    >
                      {copied ? 'Copied!' : 'Copy'}
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-700 dark:text-green-300">Your Role:</span>
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    {isHost ? 'Host' : 'Member'}
                  </span>
                </div>
              </div>
            </div>

            {/* Connected Users */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Connected Users ({connectedUsers.length})
              </h4>
              <div className="space-y-2">
                {connectedUsers.map((user) => (
                  <div key={user.id} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: user.color }}
                    ></div>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {user.name}
                      {user.id === currentUser?.id && ' (You)'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              {isHost ? (
                <>
                  <button
                    onClick={() => {
                      closeRoom();
                      handleClose();
                    }}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                  >
                    Close Room
                  </button>
                  <button
                    onClick={handleClose}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Close
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleLeaveRoom}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                  >
                    Leave Room
                  </button>
                  <button
                    onClick={handleClose}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Close
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
