# Supabase Real-Time Collaboration Setup

This guide will help you set up Supabase for real-time collaborative sharing in your L2M Boss Timer application.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up for a free account
2. Click "New Project"
3. Choose your organization
4. Enter a project name (e.g., "l2m-boss-timer")
5. Enter a database password (save this securely)
6. Select a region close to your users
7. Click "Create new project"

## 2. Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## 3. Set Up Environment Variables

1. Copy `.env.local.example` to `.env.local`:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and replace the placeholder values:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   ```

## 4. Create Database Tables

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the entire contents of `supabase-schema.sql`
3. Paste it into the SQL editor
4. Click **Run** to execute the SQL

This will create:
- `rooms` table for storing room information
- `room_users` table for tracking users in rooms
- `room_messages` table for real-time messages
- Proper indexes and Row Level Security policies
- Real-time subscriptions

## 5. Enable Real-time

1. In your Supabase dashboard, go to **Database** → **Replication**
2. Make sure the following tables are enabled for real-time:
   - `rooms`
   - `room_users` 
   - `room_messages`

If they're not enabled, click the toggle to enable them.

## 6. Test the Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Open the application in your browser
3. Try creating a room and joining from another browser/device
4. Test real-time synchronization by starting/stopping timers

## Features

### ✅ What's Now Working

- **Real cross-device collaboration** - Users can join from any device
- **Live real-time updates** - Changes sync instantly across all connected users
- **Persistent rooms** - Rooms survive browser refreshes and reconnections
- **Password protection** - Secure rooms with optional passwords
- **User presence** - See who's online in real-time
- **Automatic cleanup** - Old rooms and messages are automatically cleaned up
- **Error handling** - Robust error handling for network issues

### 🔧 Technical Improvements

- **Supabase backend** - Reliable, scalable real-time database
- **Real-time subscriptions** - Instant updates using WebSocket connections
- **Row Level Security** - Secure data access policies
- **Automatic reconnection** - Handles network interruptions gracefully
- **Optimized queries** - Efficient database operations with proper indexing

## Troubleshooting

### "Room not found" Error
- Make sure your Supabase project is set up correctly
- Check that the environment variables are correct
- Verify the database tables were created successfully

### Real-time Updates Not Working
- Check that real-time is enabled for all tables in Supabase
- Verify your network connection
- Check the browser console for any errors

### Connection Issues
- Ensure your Supabase project is active (not paused)
- Check that your API keys are correct
- Verify your internet connection

## Security Notes

- The anon key is safe to use in client-side code
- Row Level Security policies protect data access
- Passwords are hashed before storage
- Old data is automatically cleaned up

## Cost Considerations

- Supabase free tier includes:
  - Up to 500MB database storage
  - Up to 2GB bandwidth per month
  - Up to 50,000 monthly active users
  - Real-time subscriptions included

This should be more than sufficient for most boss timer sharing needs.

## Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify your Supabase setup following this guide
3. Test with a simple room creation/join flow
4. Check Supabase logs in the dashboard for any database errors
